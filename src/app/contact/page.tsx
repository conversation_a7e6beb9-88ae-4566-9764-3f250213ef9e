import { getContactPage } from '@/utils/api'
import { Metadata } from 'next'
import { HiInboxStack, HiPhone, HiEnvelope } from 'react-icons/hi2'

export async function generateMetadata(): Promise<Metadata> {
	const { title } = await getContactPage()
	return { title: title }
}

export default async function Contact() {
	const { contact, title } = await getContactPage()
	return (
		<section className="container mx-auto flex min-h-[50vh] flex-col gap-8 px-4 lg:px-12">
			<h1 className="title text-gray-800">{title}</h1>
			<div className="grid gap-8 pb-8 lg:grid-cols-2">
				{contact.map((item, index) => (
					<div key={index} className="flex gap-7 max-md:flex-col">
						{item.type !== 'other' ? (
							<a
								href={item.type === 'email' ? `mailto:${item.data}` : `tel:${item.data}`}
								className="grid h-fit w-fit place-items-center rounded-full bg-primary p-5 text-white transition duration-300 hover:bg-secondary"
							>
								{item.type === 'email' ? <HiEnvelope size={20} /> : <HiPhone size={20} />}
							</a>
						) : (
							<span className="grid h-fit w-fit place-items-center rounded-full bg-primary p-5 text-white transition duration-300 hover:bg-secondary">
								<HiInboxStack size={20} />
							</span>
						)}
						<div className="grid h-fit gap-1">
							{item.type !== 'other' ? (
								<a
									href={item.type === 'email' ? `mailto:${item.data}` : `tel:${item.data}`}
									className="w-fit border-b border-secondary border-opacity-0 text-xl font-medium text-gray-700 transition-all duration-300 hover:border-opacity-100 hover:text-secondary"
								>
									{item.data}
								</a>
							) : (
								<span className="text-xl font-medium text-gray-700 ">{item.data}</span>
							)}
							<p className="text-gray-500">{item.description}</p>
						</div>
					</div>
				))}
			</div>
		</section>
	)
}
