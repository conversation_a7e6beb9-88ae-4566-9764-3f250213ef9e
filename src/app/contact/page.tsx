import { getContactPage } from '@/utils/api'
import { Metadata } from 'next'
import { HiInboxStack, HiPhone, HiEnvelope } from 'react-icons/hi2'

export async function generateMetadata(): Promise<Metadata> {
	const { title } = await getContactPage()
	return { title: title }
}

export default async function Contact() {
	const { contact, title } = await getContactPage()
	return (
		<section className="relative min-h-screen bg-gradient-to-br from-primary/5 via-white to-secondary/5">
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,rgba(120,119,198,0.1),transparent_50%),radial-gradient(circle_at_80%_70%,rgba(255,119,198,0.1),transparent_50%)]"></div>
			<div className="container relative mx-auto px-4 py-20 lg:px-12">
				<div className="mx-auto max-w-6xl">
					<div className="mb-16 text-center">
						<div className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-primary/10 to-secondary/10 px-6 py-2 text-sm font-semibold text-primary">
							<span className="mr-2 h-2 w-2 rounded-full bg-primary"></span>
							Skontaktuj się z nami
						</div>
						<h1 className="title mb-6 bg-gradient-to-r from-primary via-gray-900 to-secondary bg-clip-text text-transparent">{title}</h1>
						<p className="mx-auto max-w-2xl text-lg leading-relaxed text-gray-600">
							Jesteśmy tutaj, aby odpowiedzieć na wszystkie Twoje pytania. Skontaktuj się z nami w dogodny dla Ciebie sposób.
						</p>
					</div>

					<div className="grid gap-8 lg:grid-cols-2">
						{contact.map((item, index) => (
							<div key={index} className="group rounded-2xl bg-white/80 p-8 shadow-xl backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:shadow-2xl">
								<div className="flex items-start gap-6">
									{item.type !== 'other' ? (
										<a
											href={item.type === 'email' ? `mailto:${item.data}` : `tel:${item.data}`}
											className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-secondary text-white shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-xl"
										>
											{item.type === 'email' ? <HiEnvelope size={24} /> : <HiPhone size={24} />}
										</a>
									) : (
										<span className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-lg">
											<HiInboxStack size={24} />
										</span>
									)}
									<div className="flex-1 space-y-3">
										{item.type !== 'other' ? (
											<a
												href={item.type === 'email' ? `mailto:${item.data}` : `tel:${item.data}`}
												className="block text-xl font-bold text-gray-800 transition-all duration-300 hover:text-primary group-hover:text-secondary"
											>
												{item.data}
											</a>
										) : (
											<span className="block text-xl font-bold text-gray-800">{item.data}</span>
										)}
										<p className="text-gray-600 leading-relaxed">{item.description}</p>
										{item.type !== 'other' && (
											<div className="mt-4">
												<a
													href={item.type === 'email' ? `mailto:${item.data}` : `tel:${item.data}`}
													className="inline-flex items-center gap-2 rounded-xl bg-gradient-to-r from-primary/10 to-secondary/10 px-4 py-2 text-sm font-semibold text-primary transition-all duration-300 hover:from-primary hover:to-secondary hover:text-white hover:scale-105"
												>
													{item.type === 'email' ? 'Wyślij email' : 'Zadzwoń teraz'}
													<svg className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
													</svg>
												</a>
											</div>
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</section>
	)
}
