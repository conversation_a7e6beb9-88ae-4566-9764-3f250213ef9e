import { Developer as DeveloperType } from '@/types'
import { getAssetPath } from '@/utils/assets'
import Image from 'next/image'
import {
	FaFacebook,
	FaMedium,
	FaGithub,
	FaInstagram,
	FaLinkedinIn,
	Fa<PERSON>wi<PERSON>,
	FaTiktok
} from 'react-icons/fa'

export const Developer = ({
	developer: { socials, img, fullName, position }
}: {
	developer: DeveloperType
}) => {
	const getSocialIcon = (name: string) => {
		switch (name) {
			case 'facebook':
				return <FaFacebook />
			case 'linkedin':
				return <FaLinkedinIn />
			case 'instagram':
				return <FaInstagram />
			case 'twitter':
				return <FaTwitter />
			case 'github':
				return <FaGithub />
			case 'medium':
				return <FaMedium />
			case 'tiktok':
				return <FaTiktok />
			default:
				return null
		}
	}
	return (
		<div className="group flex h-[30rem] w-full flex-col gap-4 rounded-lg border border-gray-300 p-2 sm:h-96 sm:w-72">
			<div className="relative h-2/3 overflow-hidden rounded">
				<Image
					src={getAssetPath(img.path)}
					alt="member"
					width={480}
					height={480}
					className="h-full w-full object-cover object-top transition-all duration-500 ease-out group-hover:scale-105"
				/>
				{socials && (
					<div className="translation-all absolute -bottom-12 left-1/2 flex w-fit -translate-x-1/2 scale-50 items-center gap-4 opacity-0 duration-300 ease-in-out group-hover:bottom-4 group-hover:scale-100 group-hover:opacity-100 ">
						{socials.map((social) => (
							<a
								key={social.type}
								href={social.url}
								className="invisible grid place-content-center rounded-full bg-white p-2 text-primary transition duration-300 hover:bg-primary hover:text-white group-hover:visible"
							>
								{getSocialIcon(social.type)}
							</a>
						))}
					</div>
				)}
			</div>
			<div className="flex flex-col gap-1">
				<h3 className="text-xl font-medium text-primary">{fullName}</h3>
				<hr className="h-0.5 w-12 bg-gradient-to-r from-primary to-secondary transition-all delay-150 duration-300 ease-out group-hover:w-24" />
				<p className="mt-2 text-sm text-gray-500">{position}</p>
			</div>
		</div>
	)
}
