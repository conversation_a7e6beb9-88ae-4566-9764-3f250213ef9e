'use client'
import { Review as ReviewType } from '@/types'
import { getAssetPath } from '@/utils/assets'
import { cn } from '@/utils/style'
import Image from 'next/image'
import { useState } from 'react'
import { HiOutlinePlay, HiVideoCamera, HiChevronRight } from 'react-icons/hi2'
import { ReviewModal } from '@/app/reviews/ReviewModal'

export const Review = ({
	review: reviewData,
	fullWidth
}: {
	review: ReviewType
	fullWidth?: boolean
}) => {
	const { fullName, position, review: reviewText, img, videoUrl, shortReview } = reviewData
	const [isModalOpen, setIsModalOpen] = useState(false)
	const displayText = shortReview || (reviewText.length > 100 ? reviewText.slice(0, 100) + '...' : reviewText)
	const hasMoreContent = shortReview || reviewText.length > 100

	const handleCardClick = (e: React.MouseEvent) => {
		if ((e.target as HTMLElement).closest('a')) {
			return
		}
		setIsModalOpen(true)
	}

	return (
		<>
			<div
				className={cn(
					'group flex w-full cursor-pointer gap-4 overflow-hidden rounded-xl border border-gray-200 bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-lg hover:shadow-gray-200/50 max-sm:flex-col sm:h-72',
					fullWidth ? 'h-[32rem] sm:w-full' : 'h-fit lg:w-[28rem]'
				)}
				onClick={handleCardClick}
			>
				
					{reviewText.length}
				{videoUrl ? (
					<a
						href={videoUrl}
						aria-label={`Watch ${fullName}'s video review`}
						className="group/video relative h-64 w-full flex-shrink-0 object-fill object-top sm:h-full sm:max-w-[12rem]"
						target="_blank"
						onClick={(e) => e.stopPropagation()}
					>
						<Image
							src={getAssetPath(img.path)}
							alt={img.description}
							width={360}
							height={360}
							className="absolute inset-0 h-full w-full rounded-lg object-cover"
						/>
						<div className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 transition duration-300 ease-out group-hover/video:opacity-100">
							<HiOutlinePlay size={40} className="text-white" />
						</div>
					</a>
				) : (
					<div className="relative h-64 w-full flex-shrink-0 object-fill object-top sm:h-full sm:max-w-[12rem]">
						<Image
							src={getAssetPath(img.path)}
							alt={img.description}
							width={360}
							height={360}
							className="absolute inset-0 h-full w-full rounded-lg object-cover"
						/>
					</div>
				)}
				<div className="flex w-full flex-col justify-between gap-4">
					<div className="space-y-3">
						<div>
							<div className="flex items-center justify-between">
								<h3 className="text-xl font-semibold text-primary">
									{fullName}
								</h3>
								{videoUrl && (
									<a
										href={videoUrl}
										aria-label={`Watch ${fullName}'s video review`}
										target="_blank"
										className="text-secondary transition-colors hover:text-secondary/80"
										onClick={(e) => e.stopPropagation()}
									>
										<HiVideoCamera size={20} />
									</a>
								)}
							</div>
							<p className="text-sm font-medium text-gray-600">{position.label}</p>
						</div>
						<p className="text-gray-700 leading-relaxed">
							{displayText}
						</p>
					</div>
					{hasMoreContent && (
						<div className="flex items-center justify-end">
							<button className="flex items-center gap-1 text-sm font-medium text-primary transition-colors group-hover:text-primary/80">
								Czytaj więcej
								<HiChevronRight size={16} className="transition-transform group-hover:translate-x-1" />
							</button>
						</div>
					)}
				</div>
			</div>

			<ReviewModal
				isOpen={isModalOpen}
				onClose={() => setIsModalOpen(false)}
				review={reviewData}
			/>
		</>
	)
}
