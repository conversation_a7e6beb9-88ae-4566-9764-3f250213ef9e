import { Review as ReviewType } from '@/types'
import { getAssetPath } from '@/utils/assets'
import { cn } from '@/utils/style'
import Image from 'next/image'
import { HiOutlinePlay, HiVideoCamera } from 'react-icons/hi2'

export const Review = ({
	review: { fullName, position, review, img, videoUrl, shortReview },
	fullWidth
}: {
	review: ReviewType
	fullWidth?: boolean
}) => {
	return (
		<div
			className={cn(
				'flex w-full gap-4 overflow-hidden rounded-lg border border-gray-300 p-2 max-sm:flex-col sm:h-72',
				fullWidth ? 'h-[32rem] sm:w-full' : 'h-fit lg:w-[28rem]'
			)}
		>
			{videoUrl ? (
				<a
					href={videoUrl}
					aria-label={`Watch ${fullName}'s video review`}
					className="group relative h-64 w-full object-fill object-top sm:h-full sm:max-w-[12rem]"
					target="_blank"
				>
					<Image
						src={getAssetPath(img.path)}
						alt={img.description}
						width={360}
						height={360}
						className="absolute inset-0 h-full w-full rounded object-cover"
					/>
					<div className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 transition duration-300 ease-out group-hover:opacity-100">
						<HiOutlinePlay size={40} className="text-white" />
					</div>
				</a>
			) : (
				<div className="relative h-64 w-full object-fill object-top sm:h-full sm:max-w-[12rem]">
					<Image
						src={getAssetPath(img.path)}
						alt={img.description}
						width={360}
						height={360}
						className="absolute inset-0 h-full w-full rounded object-cover"
					/>
				</div>
			)}
			<div className="flex w-full flex-col gap-8">
				<div>
					<h3 className="flex items-center justify-between text-xl font-medium text-primary">
						{fullName}
						{videoUrl && (
							<a
								href={videoUrl}
								aria-label={`Watch ${fullName}'s video review`}
								target="_blank"
								className="text-secondary"
							>
								<HiVideoCamera className="mr-1" />
							</a>
						)}
					</h3>
					<p className="text-gray-500">{position.label}</p>
				</div>
				<p className="line-clamp-6 text-ellipsis text-gray-500">
					{shortReview || review.length > 100 ? review.slice(0, 100) + '...' : review}
				</p>
			</div>
		</div>
	)
}
