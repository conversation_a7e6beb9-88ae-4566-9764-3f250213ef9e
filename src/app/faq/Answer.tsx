'use client'
import { useState } from 'react'
import { cn } from '@/utils/style'
import htmr from 'htmr'
import { HiChevronRight } from 'react-icons/hi2'

export const Answer = ({ question, answer }: { question: string; answer: string }) => {
	const [isOpen, setIsOpen] = useState(false)
	return (
		<div className={cn('focus-config border-b border-gray-300')}>
			<button
				className="group flex w-full items-center justify-between gap-5 rounded-md px-5 py-5 text-left"
				onClick={() => setIsOpen((prev) => !prev)}
			>
				<span className="font-medium text-gray-800 transition hover:text-secondary lg:text-lg">
					{question}
				</span>
				<div className="w-6">
					<HiChevronRight
						className={cn(
							'min-w-full transform text-primary transition duration-200 ease-in-out group-hover:text-secondary',
							isOpen && 'rotate-90'
						)}
						size={20}
					/>
				</div>
			</button>
			<div
				className={cn(
					'grid transition-all duration-200 ease-in-out',
					isOpen ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]'
				)}
			>
				<div className={cn('overflow-hidden')}>
					<div className="content px-5 pb-5 text-gray-600/90">{htmr(answer)}</div>
				</div>
			</div>
		</div>
	)
}
