import { getFAQPage } from '@/utils/api'
import { Metadata } from 'next'
import { Answer } from './Answer'

export async function generateMetadata(): Promise<Metadata> {
	const data = await getFAQPage()
	return { title: data.title }
}

export default async function Contact() {
	const data = await getFAQPage()
	return (
		<section className="container mx-auto mb-20 min-h-screen px-4 lg:px-12">
			<h1 className="title mb-10 text-gray-900">{data.title}</h1>
			<div className="flex flex-col border-t border-gray-300">
				{data.questions.map((item, index) => (
					<Answer key={index} question={item.question} answer={item.answer} />
				))}
			</div>
		</section>
	)
}
