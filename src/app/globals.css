@import url('https://use.typekit.net/qkj1rrx.css');
@import url('https://use.typekit.net/ksk4lwz.css');
@import url('https://api.fontshare.com/css?f%5B%5D=panchang');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Libre+Caslon+Text:ital,wght@0,400;0,700;1,400&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
	.no-scrollbar::-webkit-scrollbar {
		display: none;
	}
	.no-scrollbar {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
	.mac-dots {
		box-shadow:
			1.4em 0 #ec6b5e,
			2.8em 0 #f5bf4e,
			4.2em 0 #2dc943;
	}
	.title {
		@apply font-rubik text-3xl font-bold leading-tight tracking-tight sm:text-5xl sm:leading-tight;
	}
	::selection {
		@apply bg-primary text-white;
	}
	.focus-config {
		@apply rounded-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-4;
	}
	.btn-inverse {
		@apply bg-secondary hover:bg-primary;
	}
	.code {
		@apply before:mac-dots relative w-full max-w-full overflow-hidden overflow-x-auto rounded-lg bg-gray-100 p-4 text-sm text-gray-800 duration-300 before:-mx-5 before:mb-4 before:block before:h-3 before:w-3 before:rounded-full before:opacity-20 before:transition-opacity before:content-[''] hover:before:opacity-80;
	}
}

@layer components {
	.btn {
		@apply inline-flex w-fit items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200 hover:bg-secondary hover:shadow-md hover:-translate-y-0.5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 active:translate-y-0 active:shadow-sm;
	}
	.btn-md {
		@apply btn min-w-[8rem] text-base leading-7 shadow-md hover:shadow-lg;
	}
	.btn-lg {
		@apply btn rounded-lg px-8 py-4 text-base shadow-lg hover:shadow-xl;
	}
	.content {
		@apply [&>pre]:code w-full overflow-auto [&>h2]:my-8 [&>h2]:font-rubik [&>h2]:text-3xl [&>h2]:text-gray-800 [&>h2]:leading-tight [&>h3]:font-rubik [&>h3]:text-2xl [&>h3]:text-primary [&>h3]:leading-tight [&>hr]:mb-4 [&>hr]:border-gray-300 [&>img]:rounded-xl [&>img]:shadow-md [&>ol>li>a]:text-primary [&>ol>li>a]:underline [&>ol>li>a]:transition-colors [&>ol>li>a]:hover:text-secondary [&>ol]:list-inside [&>ol]:list-decimal [&>p:has(img)]:flex [&>p:has(img)]:flex-wrap [&>p:has(img)]:items-baseline [&>p:has(img)]:gap-2 [&>p>a]:text-primary [&>p>a]:underline [&>p>a]:transition-colors [&>p>a]:hover:text-secondary [&>p>img:first-child]:my-0 [&>p>img]:rounded-xl [&>p]:my-4 [&>p]:w-full [&>p]:leading-relaxed [&>pre]:my-4 [&>ul>li>a]:text-primary [&>ul>li>a]:underline [&>ul>li>a]:transition-colors [&>ul>li>a]:hover:text-secondary [&>ul]:list-inside [&>ul]:list-disc;
	}
}

html {
	scroll-behavior: smooth;
	-webkit-tap-highlight-color: transparent;
}
