import { getApplyToday } from '@/utils/api'

export default async function ApplyToday() {
	const data = await getApplyToday()
	return (
		<section
			className="relative h-96 w-full bg-headline bg-cover bg-center"
			aria-labelledby="applyTodayTitle"
		>
			<div className="absolute inset-0 bg-black/20"></div>
			<div className="container relative mx-auto grid h-full place-items-center gap-16 px-4 py-12 md:grid-cols-2 lg:px-12">
				<div className="grid gap-8">
					<h3
						id="applyTodayTitle"
						className="max-w-xs text-center text-xl font-semibold leading-tight text-white drop-shadow-lg md:max-w-md md:text-left md:text-3xl md:leading-tight"
					>
						{data.title}
					</h3>
					<p className="text-md max-w-xs text-center font-medium leading-relaxed text-white/95 drop-shadow-md md:max-w-md md:text-left md:text-xl md:leading-relaxed">
						{data.vacanciesInfo.split('{vacancies}')[0]}
						<span className="text-lg font-bold text-white md:text-2xl">{data.vacancies}</span>
						{data.vacanciesInfo.split('{vacancies}')[1]}
					</p>
				</div>
				<a
					href="/apply"
					aria-label="Apply Now"
					className="btn-lg btn-inverse mx-auto h-fit text-xl uppercase shadow-xl transition-all duration-300 hover:scale-105"
				>
					{data.cta}
				</a>
			</div>
		</section>
	)
}
