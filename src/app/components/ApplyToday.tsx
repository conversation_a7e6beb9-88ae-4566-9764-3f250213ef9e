import { getApplyToday } from '@/utils/api'

export default async function ApplyToday() {
	const data = await getApplyToday()
	return (
		<section
			className="h-96 w-full bg-headline bg-cover bg-center"
			aria-labelledby="applyTodayTitle"
		>
			<div className="container mx-auto grid h-full place-items-center gap-16 px-4 py-12 md:grid-cols-2 lg:px-12">
				<div className="grid gap-8">
					<h3
						id="applyTodayTitle"
						className="max-w-xs text-center text-xl font-semibold text-white md:max-w-md md:text-left md:text-3xl"
					>
						{data.title}
					</h3>
					<p className="text-md max-w-xs text-center font-medium text-white md:max-w-md md:text-left md:text-xl">
						{data.vacanciesInfo.split('{vacancies}')[0]}
						<span className="text-lg font-bold md:text-2xl">{data.vacancies}</span>
						{data.vacanciesInfo.split('{vacancies}')[1]}
					</p>
				</div>
				<a
					href="/apply"
					aria-label="Apply Now"
					className="btn-lg btn-inverse mx-auto h-fit text-xl uppercase shadow-md"
				>
					{data.cta}
				</a>
			</div>
		</section>
	)
}
