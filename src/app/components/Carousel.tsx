'use client'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { HiChevronRight } from 'react-icons/hi2'

export default function Carousel({
	children,
	array,
	slideWidths
}: {
	children: React.ReactNode
	array: any[]
	slideWidths: { breakpoint: number; width: number }[]
}) {
	const sliderRef = useRef<HTMLDivElement | null>(null)
	const [sliderPosition, setSliderPosition] = useState(0)
	const screenWidth = useScreenWidth()
	const slideMargin = 32
	const slideWidth = useMemo(
		() => slideWidths.find((item) => item.breakpoint <= screenWidth)?.width || slideWidths[0].width,
		[slideWidths, screenWidth]
	)

	const currentSlide = useMemo(() => {
		return Math.floor(sliderPosition / (slideWidth + slideMargin))
	}, [slideWidth, sliderPosition])

	const scrollToSlide = useCallback(
		(slider: HTMLDivElement | null, slideIndex: number) => {
			if (!slider) return
			slider.scrollTo({
				left: slideIndex * (slideWidth + slideMargin),
				behavior: 'smooth'
			})
		},
		[slideWidth, slideMargin]
	)

	const goToNextSlide = useCallback(() => {
		scrollToSlide(sliderRef.current, currentSlide + 1)
	}, [currentSlide, scrollToSlide])

	const goToPrevSlide = useCallback(() => {
		scrollToSlide(sliderRef.current, currentSlide - 1)
	}, [currentSlide, scrollToSlide])

	const scrolledToEndOfSlider =
		sliderRef.current &&
		sliderRef.current.scrollWidth - sliderRef.current.scrollLeft - sliderRef.current.clientWidth <=
			0
	return (
		<>
			<div
				className="no-scrollbar flex snap-x snap-mandatory flex-row gap-8 overflow-x-scroll"
				onScroll={(e) => setSliderPosition(e.currentTarget.scrollLeft)}
				ref={sliderRef}
			>
				{children}
			</div>

			<div className="flex w-full items-center justify-center gap-4">
				<button
					className="grid place-items-center rounded-lg bg-secondary p-2 transition-colors hover:bg-secondary/80 disabled:opacity-40 disabled:hover:bg-secondary"
					onClick={goToPrevSlide}
					disabled={currentSlide === 0 || sliderPosition === 0}
					aria-label="Slide to previous photo"
				>
					<HiChevronRight size={24} className="rotate-180 transform text-white" />
				</button>
				<button
					className="grid place-items-center rounded-lg bg-secondary p-2 transition-colors hover:bg-secondary/80 disabled:opacity-40 disabled:hover:bg-secondary"
					disabled={scrolledToEndOfSlider || currentSlide === array.length}
					onClick={goToNextSlide}
					aria-label="Slide to next photo"
				>
					<HiChevronRight size={24} className="text-white" />
				</button>
			</div>
		</>
	)
}

function useScreenWidth() {
	const [screenWidth, setScreenWidth] = useState(0)
	useEffect(() => {
		const handleResize = () => setScreenWidth(window.innerWidth)
		window.addEventListener('resize', handleResize)
		handleResize()
		return () => window.removeEventListener('resize', handleResize)
	}, [])
	return screenWidth
}
