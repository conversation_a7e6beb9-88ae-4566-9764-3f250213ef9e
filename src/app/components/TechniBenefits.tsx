import Image from 'next/image'
import Link from 'next/link'
import { getProgrammingBenefits } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'

export default async function TechniBenefits() {
	const data = await getProgrammingBenefits()
	return (
		<section
			className="container mx-auto grid scroll-mt-20 place-items-center justify-center gap-8 px-4 py-12 sm:grid-cols-2 md:py-32 lg:px-12 2xl:min-h-fit"
			aria-labelledby="techniBenefitsTitle"
		>
			<div className="grid gap-8">
				<h2 id="techniBenefitsTitle" className="title text-primary">
					{data.title}
				</h2>
				<p className="leading-relaxed text-gray-600">{data.description}</p>
				<div className="grid gap-4">
					<h3 className="text-xl font-semibold text-gray-800">{data.subtitle}</h3>
					<ul className="grid gap-3">
						{data.benefits.map((benefit, index) => (
							<li key={index} className="flex items-center gap-3 text-lg font-medium text-gray-700">
								<span className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-primary"></span>
								{benefit}
							</li>
						))}
					</ul>
				</div>
				<Link href="/curriculum" className="btn-md max-sm:w-full transition-all duration-200 hover:scale-105">
					{data.buttonText}
				</Link>
			</div>
			<div className="relative">
				<Image
					src={getAssetPath(data.img.path)}
					alt="benefits"
					height={440}
					width={440}
					className="rounded-2xl transition-transform duration-300 hover:scale-105"
				/>
			</div>
		</section>
	)
}
