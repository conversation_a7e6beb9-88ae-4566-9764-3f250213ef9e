import Image from 'next/image'
import Link from 'next/link'
import { getProgrammingBenefits } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'

export default async function TechniBenefits() {
	const data = await getProgrammingBenefits()
	return (
		<section
			className="container mx-auto grid scroll-mt-20 place-items-center justify-center gap-8 px-4 py-12 sm:grid-cols-2 md:py-32 lg:px-12 2xl:min-h-fit"
			aria-labelledby="techniBenefitsTitle"
		>
			<div className="grid gap-8">
				<h2 id="techniBenefitsTitle" className="title text-primary">
					{data.title}
				</h2>
				<p className="text-gray-500 ">{data.description}</p>
				<div className="grid gap-2">
					<h3 className="text-xl font-semibold text-gray-700">{data.subtitle}</h3>
					<ul className="grid list-disc gap-1">
						{data.benefits.map((benefit, index) => (
							<li key={index} className="ml-8 text-lg font-medium text-gray-600">
								{benefit}
							</li>
						))}
					</ul>
				</div>
				<Link href="/curriculum" className="btn-md max-sm:w-full">
					{data.buttonText}
				</Link>
			</div>
			<Image src={getAssetPath(data.img.path)} alt="benefits" height={440} width={440} />
		</section>
	)
}
