'use client'
import AvatarB<PERSON>ground from './AvatarBackground'
import Avatar from './Avatar'
import Title from './Title'
import JobTitle from './JobTitle'
import { useEffect, useState } from 'react'
import { HiChevronRight } from 'react-icons/hi2'
import { Staff } from '@/types'
import { getAssetPath } from '@/utils/assets'
import Link from 'next/link'

export default function ComposedBlocks({
	steps,
	backgroundImage,
	fullStaffText
}: {
	steps: Staff[]
	backgroundImage: string
	fullStaffText: string
}) {
	const [replay, setReplay] = useState(true)
	const [currentIndex, setCurrentIndex] = useState(0)

	const nextStep = () => {
		setReplay(false)
		setTimeout(() => {
			setCurrentIndex((prev) => (prev + 1 >= steps.length ? 0 : prev + 1))
		}, 450)
		setTimeout(() => {
			setReplay(true)
		}, 450)
	}

	const prevStep = () => {
		setReplay(false)
		setTimeout(() => {
			setCurrentIndex((prev) => (prev - 1 < 0 ? steps.length - 1 : prev - 1))
		}, 450)
		setTimeout(() => {
			setReplay(true)
		}, 450)
	}

	useEffect(() => {
		const interval = setInterval(() => {
			setReplay(false)
			setTimeout(() => {
				setCurrentIndex((prev) => (prev + 1 >= steps.length ? 0 : prev + 1))
			}, 450)
			setTimeout(() => {
				setReplay(true)
			}, 450)
		}, 4 * 1000)
		return () => clearInterval(interval)
	}, [currentIndex, replay, steps])

	return (
		<div className="relative flex h-[36rem] flex-col justify-between overflow-hidden rounded-lg border border-gray-300 bg-white lg:flex-row">
			<div className="flex w-full flex-col justify-center gap-4 pl-5 max-lg:mt-8 sm:px-16">
				<Title text={steps[currentIndex].fullName} replay={replay} />
				<JobTitle text={steps[currentIndex].jobTitle} replay={replay} />
				<div className="group z-10 flex flex-row items-center bg-white max-lg:mt-5 sm:-mt-8 lg:absolute lg:bottom-10">
					<hr className="h-0.5 w-6 rounded-full bg-primary duration-300 group-hover:bg-secondary" />
					<Link
						href="staff"
						className="ml-2 cursor-pointer text-sm text-primary duration-300 group-hover:text-secondary"
					>
						{fullStaffText}
					</Link>
				</div>
			</div>
			<div className="h-1/2 w-full lg:h-full">
				<div className="absolute left-1/2 z-[5] mt-20 grid -translate-x-1/2 -translate-y-1/2 transform place-items-center sm:mt-0 lg:left-auto lg:-ml-32 lg:h-full lg:transform-none 2xl:ml-10">
					<Avatar src={getAssetPath(steps[currentIndex].img.path)} replay={replay} />
				</div>
				<AvatarBackground replay={replay} src={backgroundImage} />
			</div>
			<button
				className="absolute right-4 top-1/2 z-20 grid -translate-y-1/2 transform place-items-center rounded-lg bg-blue-50/50 p-2 transition-colors hover:bg-blue-50/70"
				onClick={nextStep}
				aria-label="See next staff member"
			>
				<HiChevronRight size={24} className="text-primary" />
			</button>
			<button
				className="absolute left-4 top-1/2 z-20 grid -translate-y-1/2 transform place-items-center rounded-lg bg-blue-50/50 p-2 transition-colors hover:bg-blue-50/70"
				onClick={prevStep}
				aria-label="See previous staff member"
			>
				<HiChevronRight size={24} className="rotate-180 transform text-primary" />
			</button>
		</div>
	)
}
