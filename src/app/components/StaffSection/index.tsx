import ComposedBlocks from './ComposedBlocks'
import { getStaffSection } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'

export default async function Staff() {
	const { sectionData, staff } = await getStaffSection()
	return (
		<section className="container mx-auto grid h-full scroll-mt-20 gap-8 px-4 py-12 lg:place-content-center lg:px-12 2xl:min-h-fit 2xl:py-32">
			<h2 className="title text-primary">{sectionData.title}</h2>
			<p className="text-gray-500 xl:w-3/4">{sectionData.description}</p>
			<ComposedBlocks
				steps={staff}
				backgroundImage={getAssetPath(sectionData.backgroundImg.path)}
				fullStaffText={sectionData.fullStaffText}
			/>
		</section>
	)
}
