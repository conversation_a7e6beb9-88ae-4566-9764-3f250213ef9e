import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'
import Image from 'next/image'

interface Props extends HTMLMotionProps<'div'> {
	delay?: number
	replay: boolean
	duration?: number
	src: string
}

const Avatar: FC<Props> = ({ src, delay = 0.3, duration = 0.2, replay, ...props }: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const container2: Variants = {
		hidden: {
			opacity: 1
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			x: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			x: -800,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	const child2: Variants = {
		visible: {
			opacity: 1,
			x: -500,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 1,
			x: 400,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	return (
		<motion.div className="ml-7 overflow-hidden sm:ml-0">
			<motion.div
				variants={container}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="flex h-64 w-64 overflow-hidden xl:h-96 xl:w-96"
			>
				<motion.div
					variants={child}
					className="h-56 w-56 overflow-hidden rounded-lg object-cover sm:h-72 sm:w-72 xl:h-96 xl:w-96"
				>
					<Image
						src={src}
						alt="Staff Member Avatar"
						width={384}
						height={384}
						className="h-56 w-56 overflow-hidden rounded-lg object-cover sm:h-72 sm:w-72 xl:h-96 xl:w-96"
						loading="eager"
					/>
				</motion.div>
				<motion.div
					variants={container2}
					initial="hidden"
					animate={replay ? 'visible' : 'hidden'}
					{...props}
					className="absolute top-0 flex w-full overflow-hidden lg:h-full"
				>
					<motion.div variants={child2} className="z-10 h-full w-full bg-white" />
				</motion.div>
			</motion.div>
		</motion.div>
	)
}

export default Avatar
