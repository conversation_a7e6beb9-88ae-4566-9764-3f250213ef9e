'use client'
import { useState, useEffect } from 'react'
import <PERSON>Tit<PERSON> from './StepTitle'
import StepPhoto from './StepPhoto'
import StepContent from './StepContent'
import { cn } from '@/utils/style'
import { WhyTechni } from '@/types'
import { getAssetPath } from '@/utils/assets'

export default function ComposedBlocks({ steps }: { steps: WhyTechni[] }) {
	const [replay, setReplay] = useState([true, true])
	const [currentIndex, setCurrentIndex] = useState(0)

	const showStep = (index: number) => {
		setReplay([false, false])
		setTimeout(() => {
			setCurrentIndex(index)
			setReplay([true, false])
			setTimeout(() => {
				setReplay([true, true])
			}, 300)
		}, 500)
	}

	useEffect(() => {
		const interval = setInterval(() => {
			setReplay([false, false])
			setTimeout(() => {
				setCurrentIndex((prev) => (prev + 1 >= steps.length ? 0 : prev + 1))
				setReplay([true, false])
				setTimeout(() => {
					setReplay([true, true])
				}, 300)
			}, 500)
		}, 12 * 1000)
		return () => clearInterval(interval)
	}, [replay, steps])

	return (
		<>
			<div className="grid p-10 max-lg:h-full lg:w-4/6">
				<div className="mb-4 flex flex-col gap-4">
					<StepTitle text={steps[currentIndex].slideTitle} replay={replay[0]} />
					<hr className="h-1 max-w-[6rem] rounded-full bg-secondary" />
					<div className="mt-2">
						<StepContent text={steps[currentIndex].slideDescription} replay={replay[1]} />
					</div>
				</div>
				<div className="mt-12 flex flex-col justify-around sm:mt-auto sm:flex-row">
					{steps.map((step, index) => (
						<a
							href="#whyTechniTitle"
							key={step.slideShortcut}
							className={cn(
								'cursor-pointer py-2 text-center font-rubik text-sm font-medium duration-300 focus-visible:rounded-md focus-visible:outline-primary max-sm:border-l-2 max-sm:py-3 max-sm:pl-3 max-sm:text-left sm:mt-0 sm:w-1/3 sm:border-t-[1.6px] md:hidden',
								steps[currentIndex] === step
									? 'border-primary text-gray-800'
									: 'border-gray-500/80 text-gray-500/90'
							)}
							onClick={() => showStep(index)}
						>
							<span
								className={cn(
									'duration-300',
									steps[currentIndex] === step ? 'text-secondary' : 'text-gray-500/90'
								)}
							>{`0${index + 1}.`}</span>{' '}
							{step.slideShortcut}
						</a>
					))}
					{steps.map((step, index) => (
						<button
							key={step.slideShortcut + ' button' + index}
							className={cn(
								'cursor-pointer py-3 text-center font-rubik text-sm font-medium transition-all duration-300 hover:bg-gray-50 focus-visible:rounded-md focus-visible:outline-primary max-md:hidden max-sm:border-l-2 max-sm:py-3 max-sm:pl-3 max-sm:text-left sm:mt-0 sm:w-1/3 sm:border-t-[1.6px]',
								steps[currentIndex] === step
									? 'border-primary text-gray-800 bg-primary/5'
									: 'border-gray-500/90 text-gray-500/90 hover:text-gray-700'
							)}
							onClick={() => showStep(index)}
							aria-label={`Show ${step.slideShortcut} step`}
						>
							<span
								className={cn(
									'transition-all duration-300',
									steps[currentIndex] === step ? 'text-secondary font-semibold' : 'text-gray-500/90'
								)}
							>{`0${index + 1}.`}</span>{' '}
							{step.slideShortcut}
						</button>
					))}
				</div>
			</div>
			<div className="hidden w-2/6 bg-white lg:block">
				<StepPhoto replay={replay[0]} src={getAssetPath(steps[currentIndex].img.path)} />
			</div>
		</>
	)
}
