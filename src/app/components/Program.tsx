import Image from 'next/image'
import { cn } from '@/utils/style'
import { getTechniDifferences } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'

export default async function Program() {
	const { title, differences: data } = await getTechniDifferences()
	let count = 1
	const differences = data.map((section) => {
		return {
			...section,
			differences: section.differences.map((diff) => {
				return { ...diff, count: count++ }
			})
		}
	})

	return (
		<section
			className="w-full scroll-smooth bg-primary bg-shapes bg-contain py-12"
			aria-labelledby="techniDifferencesTitle"
		>
			<div className="container mx-auto mb-6 px-4 lg:px-12">
				<h2
					id="techniDifferencesTitle"
					className="w-full pb-12 font-rubik text-xl font-bold text-white sm:text-3xl lg:text-5xl"
				>
					{title}
				</h2>
			</div>
			{differences.map((section, index) => (
				<div key={index} className="container mx-auto flex px-4 lg:px-12">
					<div className={cn('w-40 border-blue-700 sm:w-60 sm:border-r-2', index === 0 && 'mt-6')}>
						<div className="sticky top-80 grid w-12 text-white" key={index}>
							<span className="text-sm font-thin sm:text-xl">0{index + 1}.</span>
							<h2 className="sm:text-semibold text-lg font-medium sm:text-2xl md:text-4xl">
								{section.title}
							</h2>
							<span className="mt-2 h-1 max-w-[3rem] rounded-full bg-secondary sm:max-w-[6rem]" />
						</div>
					</div>
					<div className="grid w-full">
						{section.differences.map((element, differenceIndex) => (
							<div
								key={differenceIndex}
								className={cn(
									'relative flex h-40 w-full sm:pl-6',
									section.differences.length - 1 === differenceIndex &&
										data.length - 1 === index &&
										'overflow-hidden'
								)}
							>
								<div className="mr-3 hidden w-3/5 pt-2 text-right text-white sm:block">
									{element.count % 2 === 0 ? (
										<Image
											src={getAssetPath(element.icon.path)}
											alt={'icon ' + element.icon.description}
											height={48}
											width={48}
											className="float-right mr-3 aspect-square h-12 object-contain"
										/>
									) : (
										<p className="pt-3">{element.description}</p>
									)}
								</div>
								<div className="pt-3">
									<svg
										width="40"
										height="40"
										className="relative z-10"
										xmlns="http://www.w3.org/2000/svg"
									>
										<circle
											cx="195"
											cy="22"
											r="10"
											transform="translate(-178)"
											fill="#ffffff"
											fillRule="evenodd"
										/>
									</svg>
									<span className="absolute -mt-4 ml-4 h-full w-0.5 bg-blue-700" />
								</div>
								<div className="ml-3 mt-2 w-40 text-white sm:w-3/5">
									{element.count % 2 === 0 ? (
										<p className="mt-3">{element.description}</p>
									) : (
										<>
											<Image
												src={getAssetPath(element.icon.path)}
												alt={'icon ' + element.icon.description}
												height={48}
												width={48}
												className="mr-3 hidden aspect-square object-contain sm:block"
											/>
											<p className="mt-3 block sm:hidden">{element.description}</p>
										</>
									)}
								</div>
							</div>
						))}
					</div>
				</div>
			))}
		</section>
	)
}
