import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'

interface Props extends HTMLMotionProps<'div'> {
	text: string
	delay?: number
	replay: boolean
	duration?: number
}

const TopBlock: FC<Props> = ({ text, delay = 0.2, duration = 0.1, replay, ...props }: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			y: -100,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	return (
		<motion.h2
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className="flex items-center justify-center overflow-hidden"
		>
			<motion.span
				variants={child}
				className="z-10 inline-block rounded-lg bg-white px-5 py-1 text-center text-lg font-bold uppercase tracking-wider text-indigo-900 sm:px-8 sm:py-2 sm:text-3xl sm:tracking-widest md:text-4xl lg:px-20"
			>
				{text}
			</motion.span>
		</motion.h2>
	)
}

export default TopBlock
