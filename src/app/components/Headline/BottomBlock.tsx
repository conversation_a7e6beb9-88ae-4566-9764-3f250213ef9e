import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'
import Link from 'next/link'

interface Pro<PERSON> extends HTMLMotionProps<'div'> {
	text?: string
	delay?: number
	replay: boolean
	duration?: number
	anchorText?: string
	anchorLink?: string
}

const BottomBlock: FC<Props> = ({
	text,
	anchorText,
	anchorLink,
	delay = 0.2,
	duration = 0.1,
	replay,
	...props
}: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			y: 100,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	return (
		<motion.h2
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className={
				'flex justify-center overflow-hidden ' +
				(anchorText ? 'absolute mt-64 h-fit sm:mt-80 md:mt-96' : 'items-center')
			}
		>
			{anchorText && anchorLink ? (
				null
			) : (
				<motion.span
					variants={child}
					className="z-10 inline-block rounded-2xl bg-white/95 px-6 py-3 text-center text-lg font-bold uppercase tracking-wider text-indigo-900 shadow-xl backdrop-blur-sm sm:px-10 sm:py-4 sm:text-2xl sm:tracking-widest md:text-3xl lg:px-16"
				>
					{text}
				</motion.span>
			)}
		</motion.h2>
	)
}

export default BottomBlock
