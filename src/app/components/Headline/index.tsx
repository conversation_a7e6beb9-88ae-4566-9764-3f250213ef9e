import { getHeroSection } from '@/utils/api'
import { cn } from '@/utils/style'
import { Montserrat } from 'next/font/google'
import { ComposedBlocks } from './ComposedBlocks'

const montserrat = Montserrat({
	subsets: ['latin-ext'],
	weight: ['300', '400', '500', '700']
})

export default async function Headline() {
	const data = await getHeroSection()
	return (
		<section
			className={cn(
				'relative grid max-h-screen place-items-center overflow-hidden bg-headline py-32 text-center transition-all md:h-screen 2xl:h-fit 2xl:py-64',
				montserrat.className
			)}
			aria-label="Techni Schools Hero Section"
		>
			<ComposedBlocks data={data} />
		</section>
	)
}
