import { getHeroSection } from '@/utils/api'
import { cn } from '@/utils/style'
import { Montserrat } from 'next/font/google'
import { ComposedBlocks } from './ComposedBlocks'

const montserrat = Montserrat({
	subsets: ['latin-ext'],
	weight: ['300', '400', '500', '700']
})

export default async function Headline() {
	const data = await getHeroSection()
	return (
		<section
			className={cn(
				'relative grid max-h-screen place-items-center overflow-hidden bg-headline py-32 text-center transition-all md:h-screen 2xl:h-fit 2xl:py-64',
				montserrat.className
			)}
			aria-label="Techni Schools Hero Section"
		>
			{/* Enhanced gradient overlays for depth */}
			<div className="absolute inset-0 bg-gradient-to-br from-black/40 via-black/20 to-black/40"></div>
			<div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>

			{/* Animated background elements */}
			<div className="absolute inset-0 opacity-30">
				<div className="absolute top-1/4 left-1/4 h-64 w-64 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-3xl animate-pulse"></div>
				<div className="absolute bottom-1/4 right-1/4 h-96 w-96 rounded-full bg-gradient-to-r from-secondary/20 to-primary/20 blur-3xl animate-pulse delay-1000"></div>
			</div>

			<ComposedBlocks data={data} />

			{/* Additional conversion elements - positioned to not affect slide transitions */}
			<div className="absolute bottom-16 left-1/2 -translate-x-1/2 flex flex-col items-center gap-4">
				{/* Social proof */}
				<div className="flex items-center gap-3 rounded-xl bg-white/10 px-6 py-3 backdrop-blur-sm">
					<div className="flex -space-x-2">
						<div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary border-2 border-white"></div>
						<div className="h-8 w-8 rounded-full bg-gradient-to-r from-secondary to-primary border-2 border-white"></div>
						<div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary border-2 border-white"></div>
					</div>
					<span className="text-sm font-semibold text-white">
						Dołącz do 500+ absolwentów
					</span>
				</div>

				{/* Urgency indicator */}
				<div className="flex items-center justify-center gap-2 text-white/80">
					<div className="h-2 w-2 rounded-full bg-red-500 animate-pulse"></div>
					<span className="text-sm font-semibold">Tylko 32 miejsca dostępne!</span>
				</div>
			</div>
		</section>
	)
}
