import { getHeroSection } from '@/utils/api'
import { cn } from '@/utils/style'
import { Montserrat } from 'next/font/google'
import { ComposedBlocks } from './ComposedBlocks'

const montserrat = Montserrat({
	subsets: ['latin-ext'],
	weight: ['300', '400', '500', '700']
})

export default async function Headline() {
	const data = await getHeroSection()
	return (
		<section
			className={cn(
				'relative grid max-h-screen place-items-center overflow-hidden bg-headline py-32 text-center transition-all md:h-screen 2xl:h-fit 2xl:py-64',
				montserrat.className
			)}
			aria-label="Techni Schools Hero Section"
		>
			{/* Enhanced gradient overlays for depth */}
			<div className="absolute inset-0 bg-gradient-to-br from-black/40 via-black/20 to-black/40"></div>
			<div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>

			{/* Animated background elements */}
			<div className="absolute inset-0 opacity-30">
				<div className="absolute top-1/4 left-1/4 h-64 w-64 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-3xl animate-pulse"></div>
				<div className="absolute bottom-1/4 right-1/4 h-96 w-96 rounded-full bg-gradient-to-r from-secondary/20 to-primary/20 blur-3xl animate-pulse delay-1000"></div>
			</div>

			<ComposedBlocks data={data} />

			{/* Conversion-focused elements */}
			<div className="absolute bottom-8 left-1/2 -translate-x-1/2 text-center">
				<div className="flex items-center justify-center gap-2 text-white/80">
					<div className="h-2 w-2 rounded-full bg-red-500 animate-pulse"></div>
					<span className="text-sm font-semibold">Tylko 32 miejsca dostępne!</span>
				</div>
			</div>
		</section>
	)
}
