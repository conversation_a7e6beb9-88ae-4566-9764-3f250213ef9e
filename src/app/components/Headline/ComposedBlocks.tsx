'use client'
import { cn } from '@/utils/style'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import BottomBlock from './BottomBlock'
import TopBlock from './TopBlock'
import MainText from './MainText'
import { HeroText } from '@/types'
import { getAssetPath } from '@/utils/assets'

export const ComposedBlocks = ({ data: { content } }: { data: HeroText }) => {
	const [replay, setReplay] = useState(true)
	const [currentIndex, setCurrentIndex] = useState(0)

	useEffect(() => {
		const interval = setInterval(() => {
			setReplay(!replay)
			setTimeout(() => {
				setReplay(true)
				setCurrentIndex((prev) => (prev + 1 >= content.length ? 0 : prev + 1))
			}, 600)
		}, 4000)
		return () => clearInterval(interval)
	}, [content, replay])

	return (
		<>
			<div
				className="pointer-events-none absolute inset-0 h-full w-full object-cover transition duration-500"
				aria-hidden="true"
			>
				{content.map((item, index) => (
					<Image
						key={index}
						src={getAssetPath(item.backgroundImg.path)}
						alt={item.backgroundImg.description}
						fill
						className={cn(
							'object-cover transition duration-500',
							content[currentIndex].mainText.toLowerCase() === item.mainText.toLowerCase()
								? 'opacity-100'
								: 'opacity-0'
						)}
						aria-hidden="true"
					/>
				))}
			</div>

			<div className="relative z-10 grid gap-8 px-4">
				<TopBlock text={content[currentIndex].topBlock} replay={replay} />
				<MainText text={content[currentIndex].mainText} replay={replay} />
				<BottomBlock text={content[currentIndex].bottomBlock} replay={replay} />

				{/* Enhanced CTA Section */}
				<div className="mt-12 flex flex-col items-center gap-6">
					{content[currentIndex].anchorText && (
						<BottomBlock
							replay={replay}
							anchorText={content[currentIndex].anchorText}
							anchorLink={content[currentIndex].anchorLink}
						/>
					)}

					{/* Additional conversion elements */}
					<div className="flex flex-col items-center gap-4 sm:flex-row sm:gap-8">
						<a
							href="#sales-form"
							className="group relative overflow-hidden rounded-2xl bg-gradient-to-r from-secondary to-secondary/80 px-8 py-4 text-lg font-bold text-white shadow-2xl transition-all duration-300 hover:scale-110 hover:shadow-3xl"
						>
							<div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
							<span className="relative z-10 flex items-center gap-2">
								Zapisz się teraz
								<svg className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
								</svg>
							</span>
						</a>

						<div className="flex items-center gap-3 rounded-xl bg-white/10 px-6 py-3 backdrop-blur-sm">
							<div className="flex -space-x-2">
								<div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary border-2 border-white"></div>
								<div className="h-8 w-8 rounded-full bg-gradient-to-r from-secondary to-primary border-2 border-white"></div>
								<div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary border-2 border-white"></div>
							</div>
							<span className="text-sm font-semibold text-white">
								Dołącz do 500+ absolwentów
							</span>
						</div>
					</div>
				</div>
			</div>
		</>
	)
}
