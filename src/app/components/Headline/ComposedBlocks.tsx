'use client'
import { cn } from '@/utils/style'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import BottomBlock from './BottomBlock'
import TopBlock from './TopBlock'
import MainText from './MainText'
import { HeroText } from '@/types'
import { getAssetPath } from '@/utils/assets'

export const ComposedBlocks = ({ data: { content } }: { data: HeroText }) => {
	const [replay, setReplay] = useState(true)
	const [currentIndex, setCurrentIndex] = useState(0)

	useEffect(() => {
		const interval = setInterval(() => {
			setReplay(!replay)
			setTimeout(() => {
				setReplay(true)
				setCurrentIndex((prev) => (prev + 1 >= content.length ? 0 : prev + 1))
			}, 600)
		}, 4000)
		return () => clearInterval(interval)
	}, [content, replay])

	return (
		<>
			<div
				className="pointer-events-none absolute inset-0 h-full w-full object-cover transition duration-500"
				aria-hidden="true"
			>
				{content.map((item, index) => (
					<Image
						key={index}
						src={getAssetPath(item.backgroundImg.path)}
						alt={item.backgroundImg.description}
						fill
						className={cn(
							'object-cover transition duration-500',
							content[currentIndex].mainText.toLowerCase() === item.mainText.toLowerCase()
								? 'opacity-100'
								: 'opacity-0'
						)}
						aria-hidden="true"
					/>
				))}
			</div>

			<div className="grid gap-8">
				<TopBlock text={content[currentIndex].topBlock} replay={replay} />
				<MainText text={content[currentIndex].mainText} replay={replay} />
				<BottomBlock text={content[currentIndex].bottomBlock} replay={replay} />
			</div>
			{content[currentIndex].anchorText && (
				<BottomBlock
					replay={replay}
					anchorText={content[currentIndex].anchorText}
					anchorLink={content[currentIndex].anchorLink}
				/>
			)}
		</>
	)
}
