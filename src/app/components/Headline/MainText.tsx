import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'

interface Pro<PERSON> extends HTMLMotionProps<'div'> {
	text: string
	delay?: number
	replay: boolean
	duration?: number
}

const MainText: FC<Props> = ({ text, delay = 0, duration = 0.1, replay, ...props }: Props) => {
	const letters = Array.from(text)

	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'spring',
				damping: 17,
				stiffness: 200
			}
		},
		hidden: {
			opacity: 0,
			y: 200,
			transition: {
				type: 'spring',
				damping: 12,
				stiffness: 200
			}
		}
	}

	return (
		<motion.h2
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className="relative flex items-center justify-center overflow-hidden"
		>
			{/* Glowing background effect */}
			<div className="absolute inset-0 bg-gradient-to-r from-primary/30 via-white/20 to-secondary/30 blur-3xl opacity-50"></div>

			{letters.map((letter, index) => (
				<motion.span
					key={index}
					variants={child}
					className="relative z-10 inline-block text-center text-5xl font-black uppercase tracking-wider text-white drop-shadow-2xl sm:text-6xl md:text-8xl lg:text-9xl"
					style={{
						textShadow: '0 0 20px rgba(255,255,255,0.5), 0 0 40px rgba(120,119,198,0.3), 0 0 60px rgba(255,119,198,0.2)'
					}}
				>
					{letter === ' ' ? '\u00A0' : letter}
				</motion.span>
			))}
		</motion.h2>
	)
}

export default MainText
