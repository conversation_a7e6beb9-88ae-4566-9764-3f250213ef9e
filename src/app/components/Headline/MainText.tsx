import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'

interface Props extends HTMLMotionProps<'div'> {
	text: string
	delay?: number
	replay: boolean
	duration?: number
}

const MainText: FC<Props> = ({ text, delay = 0, duration = 0.1, replay, ...props }: Props) => {
	const letters = Array.from(text)

	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'spring',
				damping: 17,
				stiffness: 200
			}
		},
		hidden: {
			opacity: 0,
			y: 200,
			transition: {
				type: 'spring',
				damping: 12,
				stiffness: 200
			}
		}
	}

	return (
		<motion.h2
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className="flex items-center justify-center overflow-hidden"
		>
			{letters.map((letter, index) => (
				<motion.span
					key={index}
					variants={child}
					className="m-30 z-10 inline-block text-center text-5xl font-black uppercase tracking-wider text-white sm:text-6xl md:text-8xl"
				>
					{letter === ' ' ? '\u00A0' : letter}
				</motion.span>
			))}
		</motion.h2>
	)
}

export default MainText
