import { getRecruitmentSection } from '@/utils/api'

export default async function Recruitment() {
	const { title, steps } = await getRecruitmentSection()

	return (
		<section
			id="recruitment"
			className="container mx-auto w-full scroll-mt-20 px-4 py-32 lg:min-h-fit lg:px-12"
			aria-labelledby="recruitmentTitle"
		>
			<h2 id="recruitmentTitle" className="title mb-20 tracking-tight text-primary">
				{title}
			</h2>
			<div className="relative m-auto flex justify-around gap-8 max-lg:flex-col lg:gap-16">
				{steps.map((item, index) => {
					return (
						<div key={index} className="group flex flex-row justify-center gap-8 lg:w-1/2 lg:flex-col">
							<div className="relative">
								<span className="absolute left-1/2 top-1/2 -z-10 h-full w-px -translate-x-1/2 -translate-y-1/2 transform bg-gradient-to-b from-primary/30 to-secondary/30 lg:h-px lg:w-full lg:bg-gradient-to-r" />
								<div className="relative m-auto flex h-24 w-24 place-items-center justify-center rounded-full bg-gradient-to-r from-primary to-secondary font-rubik text-4xl font-bold tabular-nums text-white shadow-2xl transition-all duration-300 group-hover:scale-110 group-hover:shadow-3xl sm:h-32 sm:w-32 sm:text-5xl">
									<div className="absolute inset-0 rounded-full bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
									<span className="relative z-10">{index + 1}</span>
								</div>
							</div>
							<div className="flex flex-col gap-4 max-lg:w-1/2">
								<h3 className="mt-4 text-lg font-bold uppercase tracking-tight text-primary transition-colors duration-300 group-hover:text-secondary sm:text-xl lg:text-center">
									{item.title}
								</h3>
								<p className="rounded-xl bg-gradient-to-r from-gray-50/50 to-white/50 p-4 text-gray-600 leading-relaxed transition-all duration-300 group-hover:from-primary/5 group-hover:to-secondary/5 group-hover:shadow-md max-sm:text-sm sm:h-40 lg:m-auto lg:w-3/4 lg:text-center">
									{item.description}
								</p>
							</div>
						</div>
					)
				})}
			</div>
		</section>
	)
}
