import { getRecruitmentSection } from '@/utils/api'

export default async function Recruitment() {
	const { title, steps } = await getRecruitmentSection()

	return (
		<section
			id="recruitment"
			className="container mx-auto w-full scroll-mt-20 px-4 py-12 lg:min-h-fit lg:px-12"
			aria-labelledby="recruitmentTitle"
		>
			<h2 id="recruitmentTitle" className="title mb-16 text-primary">
				{title}
			</h2>
			<div className="relative m-auto flex justify-around max-lg:flex-col">
				{steps.map((item, index) => {
					return (
						<div key={index} className="flex flex-row justify-center gap-8 lg:w-1/2 lg:flex-col">
							<div className="relative">
								<span className="absolute left-1/2 top-1/2 -z-10 h-full w-px -translate-x-1/2 -translate-y-1/2 transform bg-gray-300 lg:h-px lg:w-full" />
								<h2 className="m-auto flex h-24 w-24 place-items-center justify-center rounded-full border border-gray-300 bg-white font-rubik text-5xl font-bold tabular-nums text-primary sm:h-32 sm:w-32 sm:text-6xl">
									{index + 1}
								</h2>
							</div>
							<div className="flex flex-col gap-2 max-lg:w-1/2">
								<h3 className="text-md mt-4 font-semibold uppercase text-primary sm:text-lg lg:text-center">
									{item.title}
								</h3>
								<p className="text-gray-500 max-sm:text-sm sm:h-40 lg:m-auto lg:w-3/4 lg:text-center">
									{item.description}
								</p>
							</div>
						</div>
					)
				})}
			</div>
		</section>
	)
}
