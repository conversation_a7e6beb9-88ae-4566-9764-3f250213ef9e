import {
	FaFacebook,
	FaGithub,
	FaInstagram,
	FaLinkedinIn,
	FaMedium,
	FaTiktok,
	FaTwitter,
	FaYoutube
} from 'react-icons/fa'
import Logo from './Logo'
import { HiEnvelope } from 'react-icons/hi2'
import { LinkOrAnchor } from './LinkOrAnchor'
import { getFooter } from '@/utils/api'
import Link from 'next/link'
import { cn } from '@/utils/style'

export default async function Footer() {
	const data = await getFooter()
	const getSocialIcon = (name: string) => {
		switch (name) {
			case 'facebook':
				return <FaFacebook className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			case 'youtube':
				return <FaYoutube className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			case 'linkedin':
				return <FaLinkedinIn className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			case 'instagram':
				return <FaInstagram className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			case 'twitter':
				return <FaTwitter className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			case 'github':
				return <FaGithub className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			case 'medium':
				return <FaMedium className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			case 'tiktok':
				return <FaTiktok className="h-6 w-auto transition-all duration-300 group-hover:scale-110" />
			default:
				return null
		}
	}
	const currentYear = new Date().getFullYear()
	return (
		<footer className={cn('relative h-full w-full border-t border-slate-50/20 bg-footer bg-cover bg-top')}>
			<div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/40 to-black/20"></div>
			<div className="container relative mx-auto grid h-fit grid-cols-1 gap-12 px-4 py-16 text-white md:grid-cols-2 lg:px-12 xl:grid-cols-4 2xl:gap-32">
				<div className="grid place-items-center gap-6 text-center md:place-items-start md:text-left">
					<div className="w-36 transition-transform duration-300 hover:scale-105">
						<Logo alwaysWhite />
					</div>
					<p className="max-w-xs leading-relaxed text-gray-200">{data.citiesInfo}</p>
				</div>
				<div className="text-center md:text-left">
					<h2 className="mb-6 text-xl font-semibold tracking-tight">{data.menuTitle}</h2>
					<ul className="grid gap-3">
						{data.menuLinks.map((path) => (
							<li key={path.name} className="transition-all duration-200 hover:translate-x-1">
								<LinkOrAnchor
									href={path.href}
									type={path.type}
									className="text-gray-200 transition-colors duration-200 hover:text-white"
								>
									{path.name}
								</LinkOrAnchor>
							</li>
						))}
					</ul>
				</div>
				<div className="text-center md:text-left">
					<h2 className="mb-6 text-xl font-semibold tracking-tight">{data.importantLinksTitle}</h2>
					<ul className="grid gap-3">
						{data.importantLinks.map((link) => (
							<li key={link.name} className="transition-all duration-200 hover:translate-x-1">
								<LinkOrAnchor
									href={link.href}
									type={link.type}
									className="text-gray-200 transition-colors duration-200 hover:text-white"
								>
									{link.name}
								</LinkOrAnchor>
							</li>
						))}
					</ul>
				</div>
				<div className="grid text-center md:text-left xl:text-right">
					<h2 className="mb-6 text-xl font-semibold tracking-tight">{data.contactTitle}</h2>
					<div className="space-y-3">
						{data.phoneNumbers.map((phone, index) => (
							<div key={index} className="leading-relaxed text-gray-200">
								<span className="block text-sm">{phone.description}</span>
								<a
									href={`tel:${phone.number}`}
									className="font-medium text-white transition-colors duration-200 hover:text-gray-300"
								>
									{phone.number}
								</a>
							</div>
						))}
					</div>
					<div className="mt-6">
						<a
							href="mailto:<EMAIL>"
							className="group flex items-center justify-center gap-3 rounded-lg bg-white/10 px-4 py-3 backdrop-blur-sm transition-all duration-300 hover:bg-white/20 hover:scale-105 md:justify-start xl:justify-end"
						>
							<HiEnvelope size={20} className="transition-transform duration-300 group-hover:scale-110" />
							<span className="font-medium"><EMAIL></span>
						</a>
					</div>
					<div className="my-8 flex justify-center gap-3 max-sm:flex-wrap md:justify-start xl:justify-end">
						{data.socials.map((social) => (
							<a
								key={social.type}
								href={social.url}
								target="_blank"
								rel="noreferrer"
								className="group rounded-lg bg-white/10 p-3 backdrop-blur-sm transition-all duration-300 hover:bg-white/20 hover:scale-110"
								aria-label={social.type}
							>
								{getSocialIcon(social.type)}
							</a>
						))}
					</div>
				</div>
				<div className="col-span-full mt-8 grid grid-cols-1 place-items-center gap-6 border-t border-white/10 pt-8 md:grid-cols-2 md:place-items-start xl:grid-cols-4 xl:place-items-stretch">
					<span className="mt-auto text-sm font-light uppercase tracking-wider text-gray-300">
						&#169; TECHNI SCHOOLS {currentYear}
					</span>
					<Link
						href={data.devPageUrl}
						className="mt-auto text-center text-sm font-light text-gray-300 transition-colors duration-200 hover:text-white md:text-left xl:col-start-4 xl:text-right"
					>
						{data.devInfo}
					</Link>
				</div>
			</div>
		</footer>
	)
}
