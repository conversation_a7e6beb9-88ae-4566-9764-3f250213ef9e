import {
	FaFacebook,
	FaGithub,
	FaInstagram,
	FaLinkedinIn,
	FaMedium,
	FaTiktok,
	FaTwitter,
	FaYoutube
} from 'react-icons/fa'
import Logo from './Logo'
import { HiEnvelope } from 'react-icons/hi2'
import { LinkOrAnchor } from './LinkOrAnchor'
import { getFooter } from '@/utils/api'
import Link from 'next/link'
import { cn } from '@/utils/style'

export default async function Footer() {
	const data = await getFooter()
	const getSocialIcon = (name: string) => {
		switch (name) {
			case 'facebook':
				return <FaFacebook className="h-8 w-auto transition hover:text-gray-200" />
			case 'youtube':
				return <FaYoutube className="h-8 w-auto transition hover:text-gray-200" />
			case 'linkedin':
				return <FaLinkedinIn className="h-8 w-auto transition hover:text-gray-200" />
			case 'instagram':
				return <FaInstagram className="h-8 w-auto transition hover:text-gray-200" />
			case 'twitter':
				return <FaTwitter className="h-8 w-auto transition hover:text-gray-200" />
			case 'github':
				return <FaGithub className="h-8 w-auto transition hover:text-gray-200" />
			case 'medium':
				return <FaMedium className="h-8 w-auto transition hover:text-gray-200" />
			case 'tiktok':
				return <FaTiktok className="h-8 w-auto transition hover:text-gray-200" />
			default:
				return null
		}
	}
	const currentYear = new Date().getFullYear()
	return (
		<footer className={cn('h-full w-full border-t border-slate-50/10 bg-footer bg-cover bg-top')}>
			<div className="container mx-auto grid h-fit grid-cols-1 gap-10 px-4 py-12 text-white md:grid-cols-2 lg:px-12 xl:grid-cols-4 2xl:gap-28">
				<div className="grid place-items-center gap-4 text-center md:place-items-start md:text-left">
					<div className="w-32">
						<Logo alwaysWhite />
					</div>
					<p className="w-56">{data.citiesInfo}</p>
				</div>
				<div className="text-center md:text-left">
					<h2 className="mb-2 text-xl font-semibold">{data.menuTitle}</h2>
					<ul className="grid gap-1">
						{data.menuLinks.map((path) => (
							<li key={path.name} className="underline-offset-4 hover:underline">
								<LinkOrAnchor href={path.href} type={path.type}>
									{path.name}
								</LinkOrAnchor>
							</li>
						))}
					</ul>
				</div>
				<div className="text-center md:text-left">
					<h2 className="mb-2 text-xl font-semibold">{data.importantLinksTitle}</h2>
					<ul className="grid gap-1">
						{data.importantLinks.map((link) => (
							<li key={link.name} className="underline-offset-4 hover:underline">
								<LinkOrAnchor href={link.href} type={link.type}>
									{link.name}
								</LinkOrAnchor>
							</li>
						))}
					</ul>
				</div>
				<div className="grid text-center md:text-left xl:text-right">
					<h2 className="mb-2 text-xl font-semibold">{data.contactTitle}</h2>
					<p>
						{data.phoneNumbers.map((phone) => (
							<>
								{phone.description}{' '}
								<a
									href={`tel:${phone.number}`}
									className="whitespace-nowrap underline underline-offset-4"
								>
									{phone.number}
								</a>
								<br />
							</>
						))}
					</p>
					<div className="mt-4">
						<a
							href="mailto:<EMAIL>"
							className="flex items-center justify-center gap-2 md:justify-start xl:justify-end"
						>
							<HiEnvelope size={22} />{' '}
							<span className="underline underline-offset-4"><EMAIL></span>
						</a>
					</div>
					<div className="my-8 flex justify-center gap-4 max-sm:flex-wrap md:justify-start xl:justify-end">
						{data.socials.map((social) => (
							<a
								key={social.type}
								href={social.url}
								target="_blank"
								rel="noreferrer"
								className="underline-offset-4 hover:underline"
								aria-label={social.type}
							>
								{getSocialIcon(social.type)}
							</a>
						))}
					</div>
				</div>
				<div className="col-span-full grid grid-cols-1 place-items-center gap-4 md:grid-cols-2 md:place-items-start xl:grid-cols-4 xl:place-items-stretch">
					<span className="mt-auto text-sm font-light uppercase">
						&#169; TECHNI SCHOOLS {currentYear}
					</span>
					<Link
						href={data.devPageUrl}
						className="mt-auto text-center text-sm font-light underline underline-offset-4 md:text-left xl:col-start-4 xl:text-right"
					>
						{data.devInfo}
					</Link>
				</div>
			</div>
		</footer>
	)
}
