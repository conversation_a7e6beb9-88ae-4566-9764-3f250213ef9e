import { getGallerySection } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'
import Image from 'next/image'
import Link from 'next/link'
import Carousel from './Carousel'

export default async function Gallery() {
	const { sectionData, images } = await getGallerySection()
	return (
		<section>
			<div className="container mx-auto px-4 py-12 lg:px-12" aria-labelledby="galleryTitle">
				<h2 id="galleryTitle" className="title text-primary">
					{sectionData.title}
				</h2>
			</div>
			<div className="flex flex-col items-center justify-center gap-8 overflow-hidden bg-primary bg-[url('https://dev.technischools.com/assets/link/6426d06be3eb8f5fcd07e8e0')] bg-cover bg-center pb-10 pt-12">
				<div className="container mx-auto grid gap-8 px-4 lg:px-12">
					<Carousel array={images} slideWidths={[{ breakpoint: 0, width: 20 * 16 }]}>
						{images.map(({ img }, index) => (
							<div key={index}>
								<div className="relative flex aspect-[4/3] w-80 snap-start snap-always overflow-hidden rounded-lg">
									<Image
										src={getAssetPath(img.path)}
										alt={img.description}
										width={320}
										height={320}
										className="h-full w-full object-cover object-right-top"
									/>
								</div>
							</div>
						))}
					</Carousel>
				</div>
				<Link
					href="/gallery"
					className="border-b border-white border-opacity-0 font-medium text-white duration-300 hover:border-opacity-100"
				>
					{sectionData.anchorText}
				</Link>
			</div>
		</section>
	)
}
