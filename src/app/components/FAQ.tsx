import { getFAQDescription } from '@/utils/api'
import Link from 'next/link'

export default async function FAQ() {
	const data = await getFAQDescription()
	return (
		<section
			className="container mx-auto flex scroll-mt-20 flex-col items-center justify-between px-4 py-12 sm:flex-row md:px-12 lg:h-auto lg:py-20 2xl:min-h-fit 2xl:py-16"
			aria-label="FAQ Section"
		>
			<p className="mb-10 text-center text-gray-500 sm:mb-0 sm:text-left md:pr-5">
				{data.description}
			</p>
			<Link className="btn-md max-sm:w-full" href="/faq">
				FAQ
			</Link>
		</section>
	)
}
