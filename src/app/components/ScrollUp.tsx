'use client'
import { cn } from '@/utils/style'
import { useEffect, useState } from 'react'
import { HiChevronUp } from 'react-icons/hi2'

export const ScrollUp = () => {
	const [isVisible, setIsVisible] = useState(false)
	const scrollToTop = () => {
		window.scrollTo({
			top: 0,
			behavior: 'smooth'
		})
	}
	useEffect(() => {
		const toggleVisibility = () => {
			if (window.pageYOffset > 300) {
				setIsVisible(true)
			} else {
				setIsVisible(false)
			}
		}
		window.addEventListener('scroll', toggleVisibility)
		return () => window.removeEventListener('scroll', toggleVisibility)
	}, [])
	return (
		<button
			aria-label="Przewiń do góry"
			className={cn(
				'fixed bottom-4 right-4 z-30 rounded-md bg-white p-2 text-primary transition hover:bg-secondary hover:text-white',
				isVisible ? 'scale-100 opacity-100' : 'pointer-events-none scale-0 opacity-0'
			)}
			onClick={scrollToTop}
		>
			<HiChevronUp size={24} />
		</button>
	)
}
