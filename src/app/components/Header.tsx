'use client'
import { Fragment, useEffect, useState } from 'react'
import { HiBars3, HiXMark, HiChevronDown } from 'react-icons/hi2'
import Link from 'next/link'
import Logo from './Logo'
import { Menu, Transition } from '@headlessui/react'
import { usePathname } from 'next/navigation'
import { LinkOrAnchor } from './LinkOrAnchor'
import { HeaderLinks } from '@/types'
import { cn } from '@/utils/style'

export default function Header({
	data: { links, buttonText },
	customPages
}: {
	data: { links: HeaderLinks[]; buttonText: string }
	customPages?: { slug: string; transparentHeader: boolean; noMarginBottom: boolean }[]
}) {
	const pathname = usePathname()
	const isHomePage = pathname === '/'
	const [isMenuOpen, setIsMenuOpen] = useState(false)
	const { isScrollingUp, isAtTheTop } = useShowHeader()
	const isTransparent =
		isAtTheTop &&
		(isHomePage || customPages?.find((page) => pathname.includes(page.slug))?.transparentHeader)
	const isCurrentPath = (path: string) => pathname.includes(path.split('/')[1])

	const pathnameIncludes = (paths: string[]) => paths.some((path) => pathname.includes(path))
	return (
		<header
			className={cn(
				'top-0 z-30 h-20 w-full border-b border-gray-300 bg-white transition-all ease-out',
				!isScrollingUp && '-translate-y-20',
				!isScrollingUp && isMenuOpen && 'translate-y-0',
				isTransparent && 'md:border-transparent md:bg-transparent md:shadow-none',
				!isMenuOpen && isTransparent && 'border-transparent bg-transparent shadow-none',

				isHomePage || customPages?.find((page) => pathname.includes(page.slug))?.transparentHeader
					? 'fixed'
					: 'sticky mb-8',
				customPages?.find((page) => pathname.includes(page.slug))?.noMarginBottom && 'mb-0'
			)}
			aria-expanded={isMenuOpen}
		>
			<div className="container mx-auto flex h-full items-center justify-between px-4 lg:px-12">
				<Link
					href="/"
					aria-label='Go to "Home" page'
					className="focus-config mr-10 w-28 lg:mr-48 lg:w-36"
					onClick={() => setIsMenuOpen(false)}
				>
					<Logo isAtTheTop={isTransparent && !isMenuOpen} />
				</Link>
				<div className="flex items-center gap-8 md:hidden">
					<a href="/apply" className="btn-inverse btn md:hidden" aria-label="Apply Now">
						{buttonText}
					</a>
					<button
						className="focus-config"
						onClick={() => setIsMenuOpen((prev) => !prev)}
						aria-label="Toggle Menu"
					>
						{isMenuOpen ? (
							<HiXMark size={22} />
						) : (
							<HiBars3 className={cn(isTransparent && 'text-white')} size={22} />
						)}
					</button>
				</div>

				<nav
					className={cn(
						'absolute left-0 top-20 w-full overflow-y-scroll bg-white transition-all duration-300 ease-out md:static md:block md:overflow-y-visible md:bg-transparent',
						isMenuOpen ? 'h-[calc(100vh-80px)] md:h-fit' : 'h-0 md:h-full'
					)}
				>
					<ul
						className={cn(
							'container mx-auto block h-full w-full items-center justify-between px-4 transition md:flex md:px-0',
							isMenuOpen ? 'opacity-100' : 'opacity-0 md:opacity-100'
						)}
					>
						{links.map((path) => (
							<li
								key={path.name}
								className="w-full border-b border-gray-300 py-4 md:w-fit md:border-0"
							>
								{path.paths.length === 0 && path.href ? (
									<LinkOrAnchor
										href={path.href}
										type={path.type}
										className={cn(
											'focus-config flex items-center text-lg uppercase text-primary transition hover:text-secondary max-md:block md:text-sm',
											isTransparent && 'md:text-white md:hover:text-gray-300',
											isCurrentPath(path.href) && 'font-medium text-secondary'
										)}
										onClick={() => setIsMenuOpen(false)}
									>
										{path.name}
									</LinkOrAnchor>
								) : (
									<Menu as="div" className="relative block text-left md:inline-block">
										<div className="group">
											<Menu.Button
												className={cn(
													'focus-config flex w-full items-center text-lg uppercase text-primary transition hover:text-secondary group-hover:text-secondary md:inline-flex md:text-sm',
													isTransparent && 'md:text-white md:group-hover:text-gray-300',
													path.paths.some((path) => isCurrentPath(path.href)) &&
														'font-medium text-secondary'
												)}
											>
												{path.name}
												<HiChevronDown
													className={cn(
														'-mr-1 ml-2 h-5 w-5 text-primary transition group-hover:text-secondary',
														isTransparent && 'md:text-white md:group-hover:text-gray-300',
														path.paths.some((path) => isCurrentPath(path.href)) &&
															'font-medium text-secondary'
													)}
													aria-hidden="true"
												/>
											</Menu.Button>
										</div>
										<Transition
											as={Fragment}
											enter="transition ease-out duration-100"
											enterFrom="transform opacity-0 scale-95"
											enterTo="transform opacity-100 scale-100"
											leave="transition ease-in duration-75"
											leaveFrom="transform opacity-100 scale-100"
											leaveTo="transform opacity-0 scale-95"
										>
											<Menu.Items
												className={cn(
													'absolute left-0 z-30 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md border border-gray-300 bg-white shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none',
													isScrollingUp || isMenuOpen ? 'visible' : 'invisible'
												)}
											>
												<div className="px-1 py-1">
													{path.paths &&
														path.paths.map((subpath, index) => (
															<Menu.Item key={index}>
																{({ active, close }) => (
																	<LinkOrAnchor
																		href={subpath.href}
																		type={subpath.type}
																		className={cn(
																			'flex items-center',
																			active
																				? 'rounded bg-secondary text-white transition'
																				: 'text-primary',
																			'block px-4 py-2 text-sm',
																			isCurrentPath(subpath.href) &&
																				(active ? 'text-white' : 'font-medium text-secondary')
																		)}
																		onClick={() => {
																			close()
																			setIsMenuOpen(false)
																		}}
																	>
																		{subpath.name}
																	</LinkOrAnchor>
																)}
															</Menu.Item>
														))}
												</div>
											</Menu.Items>
										</Transition>
									</Menu>
								)}
							</li>
						))}
						<a href="/apply" className="btn-inverse btn max-md:hidden" aria-label="Apply Now">
							{buttonText}
						</a>
					</ul>
				</nav>
			</div>
		</header>
	)
}

function useShowHeader() {
	const [isScrollingUp, setIsScrollingUp] = useState(true)
	const [isAtTheTop, setIsAtTheTop] = useState(true)
	const headerBreakpoint = 100
	useEffect(() => {
		const threshold = 0
		let lastScrollY = window.pageYOffset
		let ticking = false

		const updateScrollDir = () => {
			const scrollY = window.pageYOffset

			if (Math.abs(scrollY - lastScrollY) < threshold) {
				ticking = false
				return
			}
			setIsScrollingUp(scrollY < lastScrollY || scrollY < headerBreakpoint)
			lastScrollY = scrollY > 0 ? scrollY : 0
			ticking = false
		}

		const onScroll = () => {
			if (!ticking) {
				window.requestAnimationFrame(updateScrollDir)
				ticking = true
			}
			if (window.pageYOffset < headerBreakpoint) {
				setIsAtTheTop(true)
			} else {
				setIsAtTheTop(false)
			}
		}

		window.addEventListener('scroll', onScroll)
		return () => window.removeEventListener('scroll', onScroll)
	}, [isScrollingUp, isAtTheTop])
	return { isScrollingUp, isAtTheTop }
}
