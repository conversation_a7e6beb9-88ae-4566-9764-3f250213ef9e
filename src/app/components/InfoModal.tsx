'use client'
import { Modal } from '@/types'
import { Dialog, Transition } from '@headlessui/react'
import { cn } from '@/utils/style'
import { Fragment, useEffect, useState } from 'react'
import { HiInformationCircle, HiPaperAirplane, HiXMark } from 'react-icons/hi2'

export default function InfoModal({ data }: { data: Modal }) {
	const [city, setCity] = useState<string>('Warszawa')
	const [phonenumber, setPhonenumber] = useState<string>('')
	const [accepted, setAccepted] = useState<boolean>(false)
	const [isLoading, setIsLoading] = useState<boolean>(false)
	const [success, setSuccess] = useState<boolean>(false)
	const [isOpen, setIsOpen] = useState(false)
	const [neverOpened, setNeverOpened] = useState(true)
	const reachedPercentage = usePercentageScrolled(data.positionPercentage || 80)
	const [customMessage, setCustomMessage] = useState<string>('')
	useEffect(() => {
		const handleScroll = () => {
			if (reachedPercentage && neverOpened) {
				openModal()
			}
		}
		window.addEventListener('scroll', handleScroll)
		return () => window.removeEventListener('scroll', handleScroll)
	}, [neverOpened, reachedPercentage])

	function closeModal() {
		setIsOpen(false)
		if (neverOpened) {
			setNeverOpened(false)
		}
		setSuccess(false)
	}

	function openModal() {
		setIsOpen(true)
	}

	function usePercentageScrolled(percentage = 80) {
		const [percentageScrolled, setPercentageScrolled] = useState(false)
		useEffect(() => {
			const handleScroll = () => {
				const scrollable = document.documentElement.scrollHeight - window.innerHeight
				const scrolled = window.scrollY
				const scrollPercent = (scrolled / scrollable) * 100
				if (scrollPercent > percentage) {
					setPercentageScrolled(true)
				} else {
					setPercentageScrolled(false)
				}
			}
			window.addEventListener('scroll', handleScroll)
			return () => window.removeEventListener('scroll', handleScroll)
		}, [])
		return percentageScrolled
	}

	async function handleForm(event: React.FormEvent<HTMLFormElement>) {
		event.preventDefault()
		if (!accepted) return
		setIsLoading(true)
		const response = await fetch('/api/popup', {
			method: 'POST',
			body: JSON.stringify({ city, phonenumber }),
			headers: {
				'Content-Type': 'application/json'
			}
		})
		setIsLoading(false)
		if (response.ok) {
			window.dataLayer.push({ enhanced_conversion_data: { phone_number: phonenumber } })
			setSuccess(true)
			setTimeout(() => {
				closeModal()
				setPhonenumber('')
			}, 2000)
		}
		if (response.status === 429) {
			setIsLoading(false)
			setSuccess(false)
			setCustomMessage('Zbyt wiele zapytań na godzinę. Proszę spróbuj ponownie później.')
		}
	}

	return (
		<>
			<button
				type="button"
				onClick={openModal}
				aria-label="Otwórz popup"
				className={cn(
					'fixed bottom-20 right-4 z-30 rounded-full bg-secondary p-3 text-white shadow-lg transition-all duration-300 hover:scale-110 hover:bg-primary hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2',
					!neverOpened ? 'scale-100 opacity-100' : 'pointer-events-none scale-0 opacity-0'
				)}
			>
				<HiInformationCircle size={24} />
			</button>

			<Transition appear show={isOpen} as={Fragment}>
				<Dialog as="div" className="relative z-50" onClose={closeModal}>
					<Transition.Child
						as={Fragment}
						enter="ease-out duration-300"
						enterFrom="opacity-0"
						enterTo="opacity-100"
						leave="ease-in duration-200"
						leaveFrom="opacity-100"
						leaveTo="opacity-0"
					>
						<div className="fixed inset-0 bg-black/60 backdrop-blur-sm" />
					</Transition.Child>

					<div className="fixed inset-0 overflow-y-auto">
						<div className="flex min-h-full items-center justify-center p-4 text-center">
							<Transition.Child
								as={Fragment}
								enter="ease-out duration-300"
								enterFrom="opacity-0 scale-95"
								enterTo="opacity-100 scale-100"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 scale-100"
								leaveTo="opacity-0 scale-95"
							>
								<Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-2xl transition-all">
									{/* Header */}
									<div className="relative bg-gradient-to-br from-primary via-primary to-primary/90 bg-shapes bg-cover bg-bottom px-8 py-10 text-white">
										<button
											className="absolute right-6 top-6 rounded-full p-1 text-white/80 transition-colors hover:bg-white/20 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/50"
											onClick={closeModal}
											aria-label="Zamknij modal"
										>
											<HiXMark size={24} />
										</button>
										<Dialog.Title
											as="h3"
											className="mb-6 text-center font-rubik text-3xl font-bold text-white sm:text-4xl"
										>
											{data.title.split('\n').map((line, index) => (
												<Fragment key={index}>
													{line}
													{index < data.title.split('\n').length - 1 && <br />}
												</Fragment>
											))}
										</Dialog.Title>
									</div>
									{/* Content */}
									<div className="px-8 py-8">
										<div className="mb-8 text-center">
											<h3 className="mb-4 text-xl font-bold text-secondary sm:text-2xl">
												{data.subtitle.split('\\n').map((line, index) => (
													<Fragment key={index}>
														{line}
														{index < data.subtitle.split('\\n').length - 1 && <br />}
													</Fragment>
												))}
											</h3>
											<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
												{data.cities.map((city) => (
													<div className="rounded-lg bg-gray-50 p-4 text-center" key={city.name}>
														<h4 className="font-semibold text-gray-900">{city.name}</h4>
														{city.date && (
															<p className="mt-1 text-sm text-gray-600">
																<span className="font-medium">Data:</span> {city.date}
															</p>
														)}
														{city.address && (
															<p className="mt-1 text-sm text-gray-600">
																<span className="font-medium">Adres:</span> {city.address}
															</p>
														)}
													</div>
												))}
											</div>
										</div>

										<div className="mb-6 text-center">
											<p className="text-sm font-medium text-gray-700 sm:text-base">
												{data.inputDescription}
											</p>
										</div>
										<form
											className="space-y-6"
											onSubmit={(e) => handleForm(e)}
										>
											<div className="grid gap-4 sm:grid-cols-2">
												<div className="space-y-2">
													<label htmlFor="city" className="block text-sm font-medium text-gray-700">
														Miasto
													</label>
													<select
														required
														value={city}
														onChange={(e) => setCity(e.target.value)}
														id="city"
														className="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 sm:text-base"
													>
														{data.cities.map((city) => (
															<option key={city.name} value={city.name}>
																{city.name}
															</option>
														))}
													</select>
												</div>
												<div className="space-y-2">
													<label htmlFor="tel" className="block text-sm font-medium text-gray-700">
														Numer telefonu
													</label>
													<input
														type="tel"
														id="tel"
														pattern="^([/+ -]*\d){9,}[/+ -]*$"
														className="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 sm:text-base"
														placeholder="Numer telefonu"
														required
														value={phonenumber}
														onChange={(e) => setPhonenumber(e.target.value)}
													/>
												</div>
											</div>

											<div className="flex items-start gap-3">
												<input
													className="mt-1 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
													type="checkbox"
													name="accept"
													id="accept"
													required
													checked={accepted}
													onChange={() => setAccepted((prev) => !prev)}
												/>
												<label htmlFor="accept" className="text-sm text-gray-600 leading-relaxed">
													{data.acceptTerms}
												</label>
											</div>

											<div className="flex justify-center">
												<button
													type="submit"
													className="btn btn-lg btn-inverse disabled:opacity-50 disabled:cursor-not-allowed"
													disabled={isLoading}
												>
													{isLoading ? 'Wysyłanie...' : data.buttonText}
													<HiPaperAirplane size={18} className="ml-2" />
												</button>
											</div>
										</form>

										{/* Success/Error Messages */}
										{success && (
											<div className="mt-6 rounded-lg bg-green-50 border border-green-200 p-4">
												<p className="text-center font-semibold text-green-800">
													{data.successMessage}
												</p>
											</div>
										)}

										{customMessage && (
											<div className="mt-6 rounded-lg bg-red-50 border border-red-200 p-4">
												<p className="text-center font-semibold text-red-800">
													{customMessage}
												</p>
											</div>
										)}

										{/* Footer */}
										<div className="mt-8 border-t border-gray-200 pt-6">
											<p className="text-center text-sm text-gray-600">
												{data.bottomInfo}
											</p>
										</div>
									</div>
								</Dialog.Panel>
							</Transition.Child>
						</div>
					</div>
				</Dialog>
			</Transition>
		</>
	)
}
