'use client'
import { Modal } from '@/types'
import { Dialog, Transition } from '@headlessui/react'
import { cn } from '@/utils/style'
import { Fragment, useEffect, useState } from 'react'
import { HiInformationCircle, HiPaperAirplane, HiXMark } from 'react-icons/hi2'

export default function InfoModal({ data }: { data: Modal }) {
	const [city, setCity] = useState<string>('Warszawa')
	const [phonenumber, setPhonenumber] = useState<string>('')
	const [accepted, setAccepted] = useState<boolean>(false)
	const [isLoading, setIsLoading] = useState<boolean>(false)
	const [success, setSuccess] = useState<boolean>(false)
	const [isOpen, setIsOpen] = useState(false)
	const [neverOpened, setNeverOpened] = useState(true)
	const reachedPercentage = usePercentageScrolled(data.positionPercentage || 80)
	const [customMessage, setCustomMessage] = useState<string>('')
	useEffect(() => {
		const handleScroll = () => {
			if (reachedPercentage && neverOpened) {
				openModal()
			}
		}
		window.addEventListener('scroll', handleScroll)
		return () => window.removeEventListener('scroll', handleScroll)
	}, [neverOpened, reachedPercentage])

	function closeModal() {
		setIsOpen(false)
		if (neverOpened) {
			setNeverOpened(false)
		}
		setSuccess(false)
	}

	function openModal() {
		setIsOpen(true)
	}

	function usePercentageScrolled(percentage = 80) {
		const [percentageScrolled, setPercentageScrolled] = useState(false)
		useEffect(() => {
			const handleScroll = () => {
				const scrollable = document.documentElement.scrollHeight - window.innerHeight
				const scrolled = window.scrollY
				const scrollPercent = (scrolled / scrollable) * 100
				if (scrollPercent > percentage) {
					setPercentageScrolled(true)
				} else {
					setPercentageScrolled(false)
				}
			}
			window.addEventListener('scroll', handleScroll)
			return () => window.removeEventListener('scroll', handleScroll)
		}, [])
		return percentageScrolled
	}

	async function handleForm(event: React.FormEvent<HTMLFormElement>) {
		event.preventDefault()
		if (!accepted) return
		setIsLoading(true)
		const response = await fetch('/api/popup', {
			method: 'POST',
			body: JSON.stringify({ city, phonenumber }),
			headers: {
				'Content-Type': 'application/json'
			}
		})
		setIsLoading(false)
		if (response.ok) {
			window.dataLayer.push({ enhanced_conversion_data: { phone_number: phonenumber } })
			setSuccess(true)
			setTimeout(() => {
				closeModal()
				setPhonenumber('')
			}, 2000)
		}
		if (response.status === 429) {
			setIsLoading(false)
			setSuccess(false)
			setCustomMessage('Zbyt wiele zapytań na godzinę. Proszę spróbuj ponownie później.')
		}
	}

	return (
		<>
			<button
				type="button"
				onClick={openModal}
				aria-label="Otwórz popup"
				className={cn(
					'fixed bottom-20 right-4 z-30 rounded-full bg-secondary p-3 text-white shadow-lg transition-all duration-300 hover:scale-110 hover:bg-primary hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2',
					!neverOpened ? 'scale-100 opacity-100' : 'pointer-events-none scale-0 opacity-0'
				)}
			>
				<HiInformationCircle size={24} />
			</button>

			<Transition appear show={isOpen} as={Fragment}>
				<Dialog as="div" className="relative z-50" onClose={closeModal}>
					<Transition.Child
						as={Fragment}
						enter="ease-out duration-300"
						enterFrom="opacity-0"
						enterTo="opacity-100"
						leave="ease-in duration-200"
						leaveFrom="opacity-100"
						leaveTo="opacity-0"
					>
						<div className="fixed inset-0 bg-black/60 backdrop-blur-sm" />
					</Transition.Child>

					<div className="fixed inset-0 overflow-y-auto">
						<div className="flex min-h-full items-center justify-center p-4 text-center">
							<Transition.Child
								as={Fragment}
								enter="ease-out duration-300"
								enterFrom="opacity-0 scale-95"
								enterTo="opacity-100 scale-100"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 scale-100"
								leaveTo="opacity-0 scale-95"
							>
								<Dialog.Panel className="transform overflow-hidden rounded-2xl bg-primary bg-shapes bg-cover bg-bottom p-8 text-left align-middle shadow-xl transition-all">
									<button
										className="absolute right-8 top-8 rounded-full p-1 text-white/80 transition-colors hover:bg-white/20 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/50"
										onClick={closeModal}
										aria-label="Zamknij modal"
									>
										<HiXMark size={24} />
									</button>
									<Dialog.Title
										as="h3"
										className="mb-4 text-center font-rubik text-2xl font-bold text-white sm:text-4xl"
									>
										{data.title.split('\n').map((line, index) => (
											<Fragment key={index}>
												{line}
												{index < data.title.split('\n').length - 1 && <br />}
											</Fragment>
										))}
									</Dialog.Title>
									<div className="flex h-full w-full flex-col items-center gap-4 rounded-lg bg-white p-4 sm:px-8 sm:py-4">
										<h3 className="text-md text-center font-bold text-secondary sm:text-xl">
											{data.subtitle.split('\\n').map((line, index) => (
												<Fragment key={index}>
													{line}
													{index < data.subtitle.split('\\n').length - 1 && <br />}
												</Fragment>
											))}
										</h3>
										<div className="flex w-full justify-around text-sm sm:text-base">
											{data.cities.map((city) => (
												<p className="text-center text-gray-600" key={city.name}>
													<span className="font-semibold text-gray-800">{city.name}: </span>
													{city.date && (
														<>
															{' '}
															+
															<br />
															{city.date}
														</>
													)}
													{city.address && (
														<>
															<br />
															{city.address}
														</>
													)}
												</p>
											))}
										</div>
										<h4 className="text-center text-xs font-semibold text-gray-600 sm:text-sm">
											{data.inputDescription}
										</h4>
										<form
											className="grid w-full place-items-center gap-4"
											onSubmit={(e) => handleForm(e)}
										>
											<div className="flex w-full gap-2 max-sm:flex-col">
												<select
													required
													value={city}
													onChange={(e) => setCity(e.target.value)}
													id="city"
													className="text-gray-60 h-10 w-full rounded-lg bg-gray-200 px-4 text-sm outline-none transition-all duration-75 focus:border-2 focus:border-primary sm:text-base"
												>
													{data.cities.map((city) => (
														<option key={city.name} value={city.name}>
															{city.name}
														</option>
													))}
												</select>
												<input
													type="tel"
													id="tel"
													pattern="^([/+ -]*\d){9,}[/+ -]*$"
													className="text-gray-60 h-10 w-full rounded-lg bg-gray-200 px-4 text-sm outline-none transition-all duration-75 focus:border-2 focus:border-primary sm:text-base"
													placeholder="Numer telefonu"
													required
													value={phonenumber}
													onChange={(e) => setPhonenumber(e.target.value)}
												/>
											</div>
											<button className="btn btn-inverse disabled:opacity-50" disabled={isLoading}>
												{isLoading ? 'Wysyłanie...' : data.buttonText} <HiPaperAirplane size={16} className="ml-2" />
											</button>
											<div className="flex items-center gap-4">
												<label className="flex items-center gap-2 text-xs text-gray-600">
													<input
														className="h-4 w-4 accent-primary"
														type="checkbox"
														name="accept"
														id="accept"
														required
														checked={accepted}
														onChange={() => setAccepted((prev) => !prev)}
													/>
													{data.acceptTerms}
												</label>
											</div>
										</form>
										<h3
											className={cn('font-bold text-green-600 last:text-lg', !success && 'hidden')}
										>
											{data.successMessage}
										</h3>
										{customMessage && (
											<h3 className={cn('font-bold text-red-600 last:text-lg')}>{customMessage}</h3>
										)}
										<h4 className="font-semibold text-gray-600">{data.bottomInfo}</h4>
									</div>
								</Dialog.Panel>
							</Transition.Child>
						</div>
					</div>
				</Dialog>
			</Transition>
		</>
	)
}
