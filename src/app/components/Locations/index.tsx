import { getLocations } from '@/utils/api'
import { Map } from './Map'

export default async function Locations() {
	const data = await getLocations()
	return (
		<section
			id="locations"
			className="min-h-[60vh] scroll-mt-20 bg-primary bg-shapes bg-right-top py-12 text-white transition-all 2xl:min-h-fit"
			aria-labelledby="locationsTitle"
		>
			<div className="container mx-auto grid h-full place-items-start gap-8 px-4 md:grid-cols-2 lg:px-12">
				<h2 id="locationsTitle" className="title w-full md:hidden">
					{data.title}
				</h2>
				<Map locations={data.locations} title={data.title} />
			</div>
		</section>
	)
}
