'use client'
import { Location } from '@/types'
import { Listbox, Transition } from '@headlessui/react'
import { cn } from '@/utils/style'
import htmr from 'htmr'
import { Fragment, useState } from 'react'
import { HiCheck, HiChevronUpDown } from 'react-icons/hi2'

export const Map = ({ locations, title }: { locations: Location[]; title: string }) => {
	const [selectedLocation, setSelectedLocation] = useState(locations[0])

	return (
		<>
			<div className="h-full">
				<div className="relative ml-auto aspect-auto w-full max-w-md">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						version="1.1"
						width="512"
						height="512"
						x="0"
						y="0"
						viewBox="0 0 3000 3000"
						className="h-full w-full"
					>
						<g>
							<path
								fillRule="evenodd"
								d="M184.755 627.026c-21.97 17.587-60.677 46.639-61.817 60.649-15.82 39.841-49.331 192.029-56.49 246.58-11.36 87.94 72.442 132.482 95.108 188.732 51.282 265.63 2.214 446.57 81.34 639.872 19.552 72.372-27.945 200.252 7.218 221.029 120.725 70.903 304.7 79.836 388.527 153.524 19.12 39.405-6.95 90.864 24.389 129.937 17.324 23.923 38.802 38.053 85.695 51.512 70.861 20.612 81.743-63.056 99.286-94.559 288.797 32.733 97.874 102.47 195.483 169.768 14.384 7.382 38.596 9.443 57.399 10.139 38.373 2.224 84.797-1.463 122.263 13.094 58.39 35.548 84.496 117.446 126.375 174.887 84.327 117.964 119.998 84.924 183.125 34.817 14.1-10.604 27.542 8.022 36.438 18.651 89.061 146.836 86.526 135.159 195.639 110.216 83.894-20.532 213.722-56.961 290.865-77.673 140.636-42.495 107.888 3.577 205.886 68.257 60.19 32.626 125.149 33.517 187.565 52.23 84.39 20.587 162.669 86.049 154.047-5.84-4.184-22.71-13.547-40.825-22.988-61.3-91.953-191.701-40.622-256.663 45.755-371.137 39.677-50.12 84.764-97.524 124.872-120.765 92.094-48.468 214.437-86.386 180.778-213.044-14.983-48.955-60.192-83.902-94.485-123.651-81.566-117.998-47.884-423.251-141.609-507.768-23.195-27.516-50.583-47.741-63.206-76.713 5.023-97.513 233.584-85.504 175.45-253.456-72.711-180.108-77.141-325.542-154.666-531.693-23.698-70.174-107.122-154.82-180.618-175.596-84.44 2.925-376.444 39.033-443.976 37.618-93.965-2.582-189.44-43.733-284.358-32.493-87.776 10.467-147.599 81.87-218.23 73.18-70.845-5.138-204.28-43.237-224.225-102.776-17.219-60.971-16.28-148.614-81.287-169.105-237.17-25.182-291.454 46.289-384.04 115.34-65.827 52.304-169.426 101.586-244.074 129.968-180.8 72.825-280.362 45.143-340.462 60.754-159.21 47.098-85.857 95.118-66.972 156.815zM2464.89 415.973c96.657 48.057 130.23 110.26 160.858 217.346 65.618 234.17 68.598 301.026 129.399 461.574 28.171 77.968-137.803 96.922-168.489 171.52-16.621 31.106-17.99 63.2 1.015 92.772 20.469 33.345 44.96 52.333 68.02 78.95 35.18 40.477 36.665 93.432 49.034 143.628 54.46 398.613 58.141 305.374 164.597 446.978 54.525 107.318-143.17 137.793-183.952 185.287-254.673 237.152-244.737 362.576-154.586 549.831-118.944-45.748-187.074-34.328-250.122-74.493-68.943-59.407-87.552-101.766-183.08-82.41-59.933 12.964-305.76 86.997-411.194 105.573-17.61-10.777-28.79-33.951-40.057-52.626-119.809-214.691-174.307 9.877-220.32-65.657-56.28-58.92-82.973-149.753-142.48-204.569-52.138-55.804-149.524-36.711-211.982-47.835 32.548-144.68-107.48-162.399-223.295-176.282-57.762-5.465-58.745 65.108-81.644 97.366-177.735-48.605 122.496-157.111-329.334-270.36-50.969-14.572-99.274-29.005-141.558-50.474 18.588-204.862 11.716-162.147-30.898-303.163-15.403-97.875-13.431-198.562-20.18-297.481-3.84-70.752-9.342-141.29-20.896-212.235-5.55-34.408-28.006-65.57-51.733-94.36-72.615-78.592-45.937-94.035-28.663-195.88 40.006-186.544 32.622-112.83 91.12-180.988 25.267-28.55-6.28-77.633-19.96-109.156 31.402-22.241 66.505-25.42 102.024-26.917 129.497 1.818 228.245-28.41 342.036-76.24 212.853-89.658 243.138-160.577 332.026-201.792 35.97-16.277 80.142-25.738 147.969-25.929 18.296 1.303 65.051-2.348 72.26 7.038 31.697 49.866 18.452 126.633 58.013 166.913 42.448 49.017 120.551 78.356 189.14 92.99 149.386 35.118 173.352-31.247 277.848-61.272 24.805-5.435 51.147-4.594 76.796-2.242 71.054 7.906 158.859 33.852 218.748 32.602 71.232-.73 328.038-29.292 433.52-38.007z"
								clipRule="evenodd"
								fill="#fff"
								data-original="#fff"
							></path>
						</g>
					</svg>
					{locations.map((location) => (
						<button
							key={location.city}
							className="absolute flex items-center gap-2 py-1.5"
							style={{
								top: `${location.position.top}%`,
								left: `${location.position.left}%`
							}}
							aria-label={`Choose ${location.city} as your location`}
							onClick={() => setSelectedLocation(location)}
						>
							<div
								className={cn(
									'aspect-square w-2 rounded-full bg-secondary transition-all',
									selectedLocation === location ? 'w-3 scale-125' : 'scale-100'
								)}
							/>
							<span
								className={cn(
									'mb-2 rounded bg-primary px-1 transition-all',
									selectedLocation === location ? 'text-lg font-semibold' : 'font-medium'
								)}
							>
								{location.city}
							</span>
						</button>
					))}
				</div>
			</div>
			<div className="flex h-full flex-col justify-start gap-8 max-md:w-full">
				<div className="hidden gap-8 md:grid">
					<h2 className="title">{title}</h2>
					<span className="h-1 max-w-xs rounded-full bg-secondary"></span>
				</div>
				<div className="sm:max-w-xs">
					<Listbox value={selectedLocation} onChange={setSelectedLocation}>
						<div className="relative mt-1">
							<Listbox.Button className="relative w-full cursor-default rounded-md bg-white py-2 pl-3 pr-10 text-left leading-7 shadow-md focus:outline-none focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-secondary sm:text-sm">
								<span className="block truncate text-black">{selectedLocation.city}</span>
								<span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
									<HiChevronUpDown className="h-5 w-5 text-gray-400" aria-hidden="true" />
								</span>
							</Listbox.Button>
							<Transition
								as={Fragment}
								enter="transition ease-out duration-100"
								enterFrom="transform opacity-0 scale-95"
								enterTo="transform opacity-100 scale-100"
								leave="transition ease-in duration-75"
								leaveFrom="transform opacity-100 scale-100"
								leaveTo="transform opacity-0 scale-95"
							>
								<Listbox.Options className="no-scrollbar absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
									{locations.map((location, index) => (
										<Listbox.Option
											key={index}
											className={({ active }) =>
												cn(
													'relative cursor-default select-none py-2 pl-10 pr-4 ',
													active || selectedLocation === location
														? 'bg-primary/10 text-primary'
														: 'text-gray-800'
												)
											}
											value={location}
										>
											<span
												className={cn(
													'block truncate',
													selectedLocation === location ? 'font-medium' : 'font-normal'
												)}
											>
												{location.city}
											</span>
											{selectedLocation === location ? (
												<span className="absolute inset-y-0 left-0 flex items-center pl-3">
													<HiCheck className="h-5 w-5 text-black text-primary/80" />
												</span>
											) : null}
										</Listbox.Option>
									))}
								</Listbox.Options>
							</Transition>
						</div>
					</Listbox>
				</div>
				<span className=" text-secondary">
					{selectedLocation.city}
					{selectedLocation.address ? `, ${selectedLocation.address}` : ''}
				</span>
				<div className="content">{htmr(selectedLocation.description)}</div>
			</div>
		</>
	)
}
