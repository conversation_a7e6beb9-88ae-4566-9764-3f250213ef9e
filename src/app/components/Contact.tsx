import { getContactSection } from '@/utils/api'
import {
	FaFacebook,
	FaGithub,
	FaInstagram,
	FaLinkedinIn,
	FaMedium,
	FaTiktok,
	FaTwitter,
	FaYoutube
} from 'react-icons/fa'
import { <PERSON><PERSON><PERSON>, <PERSON>Envelope } from 'react-icons/hi2'

export default async function Contact() {
	const data = await getContactSection()
	const getSocialIcon = (name: string) => {
		switch (name) {
			case 'facebook':
				return <FaFacebook className="h-8 w-auto text-primary duration-300 hover:text-secondary" />
			case 'youtube':
				return <FaYoutube className="h-8 w-auto text-primary duration-300 hover:text-secondary" />
			case 'linkedin':
				return (
					<FaLinkedinIn className="h-8 w-auto text-primary duration-300 hover:text-secondary" />
				)
			case 'instagram':
				return <FaInstagram className="h-8 w-auto text-primary duration-300 hover:text-secondary" />
			case 'twitter':
				return <FaTwitter className="h-8 w-auto text-primary duration-300 hover:text-secondary" />
			case 'github':
				return <FaGithub className="h-8 w-auto text-primary duration-300 hover:text-secondary" />
			case 'medium':
				return <FaMedium className="h-8 w-auto text-primary duration-300 hover:text-secondary" />
			case 'tiktok':
				return <FaTiktok className="h-8 w-auto text-primary duration-300 hover:text-secondary" />

			default:
				return null
		}
	}
	return (
		<section
			className="container mx-auto mt-10 flex scroll-mt-20 items-center justify-between gap-8 px-4 py-12 max-lg:flex-col lg:px-12"
			aria-label="Contact"
		>
			<div className="flex flex-wrap items-center justify-center gap-8">
				{data.socials.map((social) => (
					<a
						key={social.type}
						href={social.url}
						rel="noreferrer"
						target="blank"
						className="focus-config"
						aria-label={social.type}
					>
						{getSocialIcon(social.type)}
					</a>
				))}
			</div>
			<div className="flex flex-col items-center justify-between gap-8 sm:w-auto md:flex-row">
				<div className="flex w-full flex-col items-center gap-8 md:flex-row xl:justify-around">
					<p className="text-primary">{data.callText}</p>
					<a
						href={`tel:${data.phone}`}
						className="btn-md flex gap-2 max-md:w-full"
						aria-label="{`Call ${data.phone}`}"
					>
						<div className="grid h-5 w-5 place-items-center">
							<HiPhone className="min-w-full" />
						</div>
						{data.phone}
					</a>
				</div>
				<div className="flex w-full flex-col items-center gap-8 md:flex-row md:justify-between">
					<p className="text-primary">{data.emailText}</p>
					<a
						href={`mailto:${data.email}`}
						className="btn-md flex gap-2 max-md:w-full"
						aria-label={`Email ${data.email}`}
					>
						<div className="mr-1 grid h-5 w-5 place-items-center">
							<HiEnvelope className="min-w-full" />
						</div>
						{data.email}
					</a>
				</div>
			</div>
		</section>
	)
}
